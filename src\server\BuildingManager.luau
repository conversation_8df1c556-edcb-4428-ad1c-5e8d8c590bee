--[[
	Building Manager
	Server-side logic for building placement, upgrades, and management
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local ServerStorage = game:GetService("ServerStorage")

local Config = require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Config"))
local BuildingSystem = require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("BuildingSystem"))
local DataManager = require(script.Parent:WaitForChild("DataManager"))
local PlotManager = require(script.Parent:WaitForChild("PlotManager"))
local SoundEvents = require(script.Parent:WaitForChild("SoundEvents"))

-- Get RemoteEvents with direct access approach
local Assets = ReplicatedStorage:WaitForChild("Assets")

-- Wait for RemoteEvents module to exist
local RemoteEventsModule = Assets:WaitF<PERSON><PERSON>hild("RemoteEvents")
local RemoteFunctionsModule = Assets:WaitForChild("RemoteFunctions")

-- Load modules
local RemoteEvents = require(RemoteEventsModule)
local RemoteFunctions = require(RemoteFunctionsModule)

-- Function to wait for specific RemoteEvent to be created
local function waitForRemoteEvent(eventName)
	local event = Assets:WaitForChild(eventName, 10) -- Wait up to 10 seconds
	if event then
		print("✅ Found RemoteEvent:", eventName)
		return event
	else
		warn("❌ RemoteEvent not found after 10 seconds:", eventName)
		return nil
	end
end

-- Wait for all required RemoteEvents to be created in Assets folder
print("🔍 Waiting for RemoteEvents to be created...")
local requiredEvents = {
	"PlaceBuilding",
	"UpgradeBuilding",
	"RemoveBuilding",
	"BuildingPlaced",
	"BuildingUpgraded",
	"BuildingRemoved",
	"ShowNotification"
}

for _, eventName in ipairs(requiredEvents) do
	waitForRemoteEvent(eventName)
end

print("✅ All RemoteEvents verified and ready!")

local BuildingManager = {}

-- Building containers
local BuildingsFolder = Workspace:WaitForChild("Buildings")
local RoadsFolder = Workspace:WaitForChild("Roads")

-- Player state tracking for building/removal modes
local PlayerStates = {}

-- Place a building with rotation support (with plot integration)
function BuildingManager.PlaceBuilding(player, buildingType, position, rotation)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end

	-- Convert world position to grid
	local gridPosition = BuildingSystem.WorldToGrid(position)

	-- Check if player can build this building
	local canBuild, reason = BuildingSystem.CanBuild(buildingType, playerData)
	if not canBuild then
		return false, reason
	end

	-- Get player's plot information
	local playerPlotInfo = PlotManager.GetPlayerPlotInfo(player)
	if not playerPlotInfo then
		return false, "No plot assigned. Please claim a plot first!"
	end

	-- Check placement validity (with plot validation)
	local canPlace, placeReason = BuildingSystem.CanPlaceBuilding(
		buildingType,
		gridPosition,
		playerData.Buildings,
		playerData.Roads,
		playerPlotInfo -- Pass plot info for validation
	)
	if not canPlace then
		return false, placeReason
	end

	-- Additional plot boundary check
	if not PlotManager.IsPositionInPlayerPlot(player, position) then
		return false, "Building must be placed within your plot boundaries!"
	end

	-- Get building config
	local buildingConfig = Config.BUILDINGS[buildingType]

	-- Spend resources
	if not DataManager.SpendResources(player, buildingConfig.Cost) then
		return false, "Insufficient resources"
	end

	-- Create building data with rotation
	local buildingId = tostring(tick()) .. "_" .. tostring(player.UserId)
	local worldPosition = BuildingSystem.GridToWorld(gridPosition)

	local buildingData = {
		Id = buildingId,
		Type = buildingType,
		Position = {X = worldPosition.X, Y = worldPosition.Y, Z = worldPosition.Z}, -- Serialize Vector3
		GridPosition = {X = gridPosition.X, Y = gridPosition.Y}, -- Serialize Vector2
		Rotation = rotation or 0, -- Store rotation
		Level = 1,
		Owner = player.UserId,
		PlacedAt = tick(),
		Health = 100,
		Active = true
	}
	
	-- Add to player data
	playerData.Buildings[buildingId] = buildingData
	
	-- Create physical building
	BuildingManager.CreateBuildingModel(buildingData)

	-- Add building to player's plot
	PlotManager.AddBuildingToPlot(player, buildingId, buildingType, position)

	-- Update population and production
	BuildingManager.UpdateCityStats(player)

	-- Award XP
	DataManager.AddToPlayer(player, "XP", Config.XP_REWARDS.BUILD_BUILDING)

	-- Notify client
	RemoteEvents.BuildingPlaced:FireClient(player, buildingData)

	-- Trigger sound event
	SoundEvents.OnBuildingPlaced(player, buildingData)

	return true, "Building placed successfully"
end

-- Upgrade a building
function BuildingManager.UpgradeBuilding(player, buildingId)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end
	
	local building = playerData.Buildings[buildingId]
	if not building then
		return false, "Building not found"
	end
	
	-- Get upgrade cost
	local upgradeCost = BuildingSystem.GetUpgradeCost(building.Type, building.Level)
	if not upgradeCost then
		return false, "Cannot upgrade this building"
	end
	
	-- Check if player can afford upgrade
	if not DataManager.CanAfford(player, upgradeCost) then
		return false, "Cannot afford upgrade"
	end
	
	-- Spend resources
	if not DataManager.SpendResources(player, upgradeCost) then
		return false, "Failed to spend resources"
	end
	
	-- Upgrade building
	building.Level = building.Level + 1
	building.UpgradedAt = tick()
	
	-- Update physical model
	BuildingManager.UpdateBuildingModel(building)
	
	-- Update city stats
	BuildingManager.UpdateCityStats(player)
	
	-- Award XP
	DataManager.AddToPlayer(player, "XP", Config.XP_REWARDS.UPGRADE_BUILDING)
	
	-- Notify client
	RemoteEvents.BuildingUpgraded:FireClient(player, building)

	-- Trigger sound event
	SoundEvents.OnBuildingUpgraded(player, building)

	return true, "Building upgraded successfully"
end

-- Delete multiple buildings
function BuildingManager.DeleteBuildings(player, buildingIds)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end

	if not buildingIds or #buildingIds == 0 then
		return false, "No buildings specified for deletion"
	end

	local deletedCount = 0
	local totalRefund = {Pieces = 0, Cash = 0, Metal = 0, Plastic = 0}

	print("🗑️ Deleting", #buildingIds, "buildings for player", player.Name)

	for _, buildingId in ipairs(buildingIds) do
		local building = playerData.Buildings[buildingId]
		if building then
			-- Enhanced ownership validation
			if building.Owner ~= player.UserId then
				warn("🚫 Security: Player", player.Name, "tried to delete building owned by", building.Owner)
				continue
			end

			-- Calculate refund (50% of original cost)
			local buildingConfig = Config.BUILDINGS[building.Type]
			if buildingConfig and buildingConfig.Cost then
				for currency, amount in pairs(buildingConfig.Cost) do
					local refundAmount = math.floor(amount * 0.5) -- 50% refund
					totalRefund[currency] = (totalRefund[currency] or 0) + refundAmount
				end
			end

			-- Remove building model from world
			local modelName = building.Type .. "_" .. buildingId
			local model = BuildingsFolder:FindFirstChild(modelName)
			if model then
				model:Destroy()
				print("🗑️ Removed building model:", modelName)
			end

			-- Remove from player data
			playerData.Buildings[buildingId] = nil
			deletedCount = deletedCount + 1

			-- Remove from plot if applicable
			PlotManager.RemoveBuildingFromPlot(player, buildingId)

			print("🗑️ Deleted building:", buildingId, "Type:", building.Type)
		else
			warn("🗑️ Building not found for deletion:", buildingId)
		end
	end

	-- Give refund to player
	for currency, amount in pairs(totalRefund) do
		if amount > 0 then
			DataManager.AddToPlayer(player, currency, amount)
			print("🗑️ Refunded", amount, currency, "to", player.Name)
		end
	end

	-- Update city stats
	BuildingManager.UpdateCityStats(player)

	-- Save player data
	DataManager.SavePlayerData(player)

	-- Notify client
	if RemoteEvents.BuildingsDeleted then
		RemoteEvents.BuildingsDeleted:FireClient(player, buildingIds, totalRefund)
	end

	print("🗑️ Successfully deleted", deletedCount, "buildings for", player.Name)
	return true, "Deleted " .. deletedCount .. " buildings. Refunded resources."
end

-- Load building model from storage (ReplicatedStorage first, then ServerStorage)
function BuildingManager.LoadBuildingModel(buildingType)
	-- Try ReplicatedStorage first for client-accessible models
	local replicatedModels = ReplicatedStorage:FindFirstChild("BuildingModels")
	if replicatedModels then
		local model = replicatedModels:FindFirstChild(buildingType)
		if model then
			print("🏗️ Found building model in ReplicatedStorage:", buildingType)
			return model
		end
	end

	-- Try ServerStorage for server-only models
	local serverModels = ServerStorage:FindFirstChild("Models")
	if serverModels then
		local model = serverModels:FindFirstChild(buildingType)
		if model then
			print("🏗️ Found building model in ServerStorage:", buildingType)
			return model
		end
	end

	warn("🏗️ Building model not found:", buildingType)
	return nil
end

-- Create physical building model with SimCity-style placement
function BuildingManager.CreateBuildingModel(buildingData)
	local buildingConfig = Config.BUILDINGS[buildingData.Type]
	if not buildingConfig then
		warn("🏗️ Building config not found for:", buildingData.Type)
		return nil
	end

	-- Try to load actual model first
	local buildingModel = BuildingManager.LoadBuildingModel(buildingData.Type)
	local model

	if buildingModel then
		-- Clone the actual model
		model = buildingModel:Clone()
		print("🏗️ Using actual model for:", buildingData.Type)
	else
		-- Create fallback model
		model = BuildingManager.CreateFallbackModel(buildingData, buildingConfig)
		print("🏗️ Using fallback model for:", buildingData.Type)
	end

	-- Set model name and properties
	model.Name = buildingData.Type .. "_" .. buildingData.Id

	-- Enable collision for all building parts
	BuildingManager.EnableBuildingCollision(model)

	-- Position and align the building with SimCity-style grid snapping
	BuildingManager.PositionBuildingModel(model, buildingData, buildingConfig)

	-- Add comprehensive building data
	BuildingManager.AddBuildingData(model, buildingData, buildingConfig)

	-- Parent to workspace
	model.Parent = BuildingsFolder

	print("🏗️ Created building model:", buildingData.Type, "at", buildingData.Position.X, buildingData.Position.Z)
	return model
end

-- Create fallback model when actual model is not found
function BuildingManager.CreateFallbackModel(buildingData, buildingConfig)
	local model = Instance.new("Model")
	model.Name = "Fallback_" .. buildingData.Type

	local part = Instance.new("Part")
	part.Name = "Base"
	part.Anchored = true
	part.CanCollide = true
	part.Material = Enum.Material.Concrete

	-- Set size based on building config
	if buildingConfig.Size then
		part.Size = Vector3.new(buildingConfig.Size[1], buildingConfig.Size[2], buildingConfig.Size[3])
	else
		part.Size = Vector3.new(4, 4, 4)
	end

	-- Set color based on building type with better materials
	if buildingConfig.Type == Config.BUILDING_TYPES.RESIDENTIAL then
		part.Color = Color3.new(0.8, 0.6, 0.4) -- Brown
		part.Material = Enum.Material.Brick
	elseif buildingConfig.Type == Config.BUILDING_TYPES.COMMERCIAL then
		part.Color = Color3.new(0.6, 0.8, 0.9) -- Light Blue
		part.Material = Enum.Material.Glass
	elseif buildingConfig.Type == Config.BUILDING_TYPES.INDUSTRIAL then
		part.Color = Color3.new(0.6, 0.6, 0.6) -- Gray
		part.Material = Enum.Material.Metal
	elseif buildingConfig.Type == Config.BUILDING_TYPES.UTILITY then
		part.Color = Color3.new(0.5, 0.5, 0.8) -- Blue
		part.Material = Enum.Material.Neon
	elseif buildingConfig.Type == Config.BUILDING_TYPES.SERVICE then
		part.Color = Color3.new(0.8, 0.8, 0.2) -- Yellow
		part.Material = Enum.Material.SmoothPlastic
	else
		part.Color = Color3.new(0.8, 0.8, 0.8) -- White
		part.Material = Enum.Material.Plastic
	end

	part.Parent = model
	return model
end

-- Enable collision for all building parts
function BuildingManager.EnableBuildingCollision(model)
	-- Enable collision for all parts in the model
	for _, descendant in pairs(model:GetDescendants()) do
		if descendant:IsA("BasePart") then
			descendant.CanCollide = true
			descendant.Anchored = true

			-- Ensure parts are not too transparent for visibility
			if descendant.Transparency > 0.8 then
				descendant.Transparency = 0.3
			end

			-- Add collision sound if not present
			if not descendant:FindFirstChild("CollisionSound") then
				local sound = Instance.new("Sound")
				sound.Name = "CollisionSound"
				sound.SoundId = "rbxasset://sounds/impact_generic.mp3"
				sound.Volume = 0.5
				sound.Parent = descendant
			end
		end
	end

	print("🏗️ Enabled collision for building model:", model.Name)
end

-- Position building model with SimCity-style grid alignment
function BuildingManager.PositionBuildingModel(model, buildingData, buildingConfig)
	-- Convert serialized position back to Vector3
	local position = Vector3.new(buildingData.Position.X, buildingData.Position.Y, buildingData.Position.Z)
	local rotation = buildingData.Rotation or 0

	-- Get the primary part (Base part or PrimaryPart)
	local primaryPart = model:FindFirstChild("Base") or model.PrimaryPart
	if not primaryPart then
		-- If no primary part found, create one
		primaryPart = model:FindFirstChildOfClass("Part")
		if primaryPart then
			model.PrimaryPart = primaryPart
		end
	end

	if primaryPart then
		-- Calculate proper Y position (ground level + half height)
		local yPosition = position.Y + (primaryPart.Size.Y / 2)
		local finalPosition = Vector3.new(position.X, yPosition, position.Z)

		-- Apply position and rotation with grid alignment
		local cframe = CFrame.new(finalPosition) * CFrame.Angles(0, math.rad(rotation), 0)

		if model.PrimaryPart then
			model:SetPrimaryPartCFrame(cframe)
		else
			primaryPart.CFrame = cframe
		end

		print("🏗️ Positioned building at:", finalPosition, "rotation:", rotation)
	else
		warn("🏗️ No primary part found for building model:", buildingData.Type)
	end
end

-- Add comprehensive building data to model
function BuildingManager.AddBuildingData(model, buildingData, buildingConfig)
	-- Building ID
	local buildingInfo = Instance.new("StringValue")
	buildingInfo.Name = "BuildingInfo"
	buildingInfo.Value = buildingData.Id
	buildingInfo.Parent = model

	-- Building Type
	local buildingType = Instance.new("StringValue")
	buildingType.Name = "BuildingType"
	buildingType.Value = buildingData.Type
	buildingType.Parent = model

	-- Building Name
	local buildingName = Instance.new("StringValue")
	buildingName.Name = "BuildingName"
	buildingName.Value = buildingConfig.Name or buildingData.Type
	buildingName.Parent = model

	-- Building Description
	local description = Instance.new("StringValue")
	description.Name = "Description"
	description.Value = buildingConfig.Description or ("A " .. buildingConfig.Name .. " building")
	description.Parent = model

	-- Building Level
	local level = Instance.new("IntValue")
	level.Name = "Level"
	level.Value = buildingData.Level or 1
	level.Parent = model

	-- Building Health
	local health = Instance.new("NumberValue")
	health.Name = "Health"
	health.Value = buildingData.Health or 100
	health.Parent = model

	-- Owner Information
	local owner = Instance.new("IntValue")
	owner.Name = "Owner"
	owner.Value = buildingData.Owner
	owner.Parent = model

	-- Building Stats (Population, Energy, etc.)
	if buildingConfig.Population then
		local population = Instance.new("IntValue")
		population.Name = "Population"
		population.Value = buildingConfig.Population * (buildingData.Level or 1)
		population.Parent = model
	end

	if buildingConfig.EnergyProduction then
		local energy = Instance.new("NumberValue")
		energy.Name = "EnergyProduction"
		energy.Value = buildingConfig.EnergyProduction * (buildingData.Level or 1)
		energy.Parent = model
	end

	if buildingConfig.EnergyConsumption then
		local energyConsumption = Instance.new("NumberValue")
		energyConsumption.Name = "EnergyConsumption"
		energyConsumption.Value = buildingConfig.EnergyConsumption * (buildingData.Level or 1)
		energyConsumption.Parent = model
	end

	-- Add click detector for interaction
	local clickDetector = Instance.new("ClickDetector")
	clickDetector.MaxActivationDistance = 50
	clickDetector.Parent = model.PrimaryPart or model:FindFirstChildOfClass("Part")

	-- Handle building interaction with enhanced ownership validation
	clickDetector.MouseClick:Connect(function(player)
		-- Check if player is in removal mode
		local playerState = PlayerStates[player.UserId]
		if playerState and playerState.removalMode then
			-- Enhanced ownership validation
			local playerData = DataManager.GetPlayerData(player)
			if not playerData then
				local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
				if showNotificationEvent then
					showNotificationEvent:FireClient(player, "Error", "Player data not loaded!")
				end
				return
			end

			-- Check if building exists in player's data
			local building = playerData.Buildings[buildingData.Id]
			if building and building.Owner == player.UserId then
				-- Player owns this building, remove it
				local success, message = BuildingManager.RemoveBuilding(player, buildingData.Id)
				local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
				if showNotificationEvent then
					if success then
						showNotificationEvent:FireClient(player, "Success", message)
						-- Exit removal mode after successful removal
						PlayerStates[player.UserId].removalMode = false
					else
						showNotificationEvent:FireClient(player, "Error", message)
					end
				end
			else
				-- Player doesn't own this building - enhanced security check
				local actualOwner = "Unknown"
				if building and building.Owner then
					local ownerPlayer = Players:GetPlayerByUserId(building.Owner)
					actualOwner = ownerPlayer and ownerPlayer.Name or "Player " .. building.Owner
				end

				local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
				if showNotificationEvent then
					showNotificationEvent:FireClient(player, "Error", "You don't own this building! Owner: " .. actualOwner)
				end

				print("🚫 Security: Player", player.Name, "tried to remove building owned by", actualOwner)
			end
		else
			-- Normal building interaction
			BuildingManager.InteractWithBuilding(player, buildingData.Id)
		end
	end)

	print("🏗️ Added comprehensive data to building:", buildingData.Type)
end

-- Remove building from the game with enhanced security
function BuildingManager.RemoveBuilding(player, buildingId)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end

	local building = playerData.Buildings[buildingId]
	if not building then
		return false, "Building not found in your buildings"
	end

	-- Enhanced ownership validation
	if building.Owner ~= player.UserId then
		local actualOwner = "Unknown"
		if building.Owner then
			local ownerPlayer = Players:GetPlayerByUserId(building.Owner)
			actualOwner = ownerPlayer and ownerPlayer.Name or "Player " .. building.Owner
		end

		print("🚫 Security: Player", player.Name, "tried to remove building owned by", actualOwner)
		return false, "You don't own this building! Owner: " .. actualOwner
	end

	-- Remove physical model
	local modelName = building.Type .. "_" .. buildingId
	local model = BuildingsFolder:FindFirstChild(modelName)
	if model then
		model:Destroy()
		print("🏗️ Removed building model:", modelName)
	end

	-- Remove from player data
	playerData.Buildings[buildingId] = nil

	-- Remove from plot if applicable
	PlotManager.RemoveBuildingFromPlot(player, buildingId)

	-- Update city stats
	BuildingManager.UpdateCityStats(player)

	-- Refund partial resources (50% of original cost)
	local buildingConfig = Config.BUILDINGS[building.Type]
	if buildingConfig and buildingConfig.Cost then
		local refund = {}
		for currency, amount in pairs(buildingConfig.Cost) do
			refund[currency] = math.floor(amount * 0.5) -- 50% refund
		end

		for currency, amount in pairs(refund) do
			DataManager.AddToPlayer(player, currency, amount)
		end

		print("🏗️ Refunded:", refund, "to player:", player.Name)
	end

	-- Notify client
	RemoteEvents.BuildingRemoved:FireClient(player, buildingId)

	return true, "Building removed successfully"
end

-- Get building info for interaction
function BuildingManager.GetBuildingInfo(buildingId)
	-- Find the building model in workspace
	for _, model in pairs(BuildingsFolder:GetChildren()) do
		local buildingInfo = model:FindFirstChild("BuildingInfo")
		if buildingInfo and buildingInfo.Value == buildingId then
			local info = {
				Id = buildingId,
				Type = model:FindFirstChild("BuildingType") and model.BuildingType.Value or "Unknown",
				Name = model:FindFirstChild("BuildingName") and model.BuildingName.Value or "Unknown Building",
				Description = model:FindFirstChild("Description") and model.Description.Value or "No description available",
				Level = model:FindFirstChild("Level") and model.Level.Value or 1,
				Health = model:FindFirstChild("Health") and model.Health.Value or 100,
				Position = model.PrimaryPart and model.PrimaryPart.Position or Vector3.new(0, 0, 0)
			}
			return info
		end
	end

	return nil
end

-- Update building model (for upgrades)
function BuildingManager.UpdateBuildingModel(buildingData)
	local modelName = buildingData.Type .. "_" .. buildingData.Id
	local model = BuildingsFolder:FindFirstChild(modelName)
	
	if model then
		local base = model:FindFirstChild("Base")
		if base then
			-- Increase size slightly for each level
			local buildingConfig = Config.BUILDINGS[buildingData.Type]
			local baseSize = buildingConfig.Size or {4, 4, 4}
			local levelMultiplier = 1 + (buildingData.Level - 1) * 0.1
			
			base.Size = Vector3.new(
				baseSize[1] * levelMultiplier,
				baseSize[2] * levelMultiplier,
				baseSize[3] * levelMultiplier
			)
			
			-- Make it slightly brighter for higher levels
			local brightness = math.min(1, 0.8 + buildingData.Level * 0.05)
			base.Color = base.Color:lerp(Color3.new(1, 1, 1), brightness - 0.8)
		end
	end
end

-- Update city statistics with enhanced water and electricity tracking
function BuildingManager.UpdateCityStats(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end

	local totalPopulation = 0
	local energyProduction = 0
	local waterProduction = 0
	local energyConsumption = 0
	local waterConsumption = 0

	-- Calculate totals from all buildings
	for _, building in pairs(playerData.Buildings) do
		if building.Active then
			local config = Config.BUILDINGS[building.Type]
			if config then
				-- Population
				if config.Population then
					totalPopulation += config.Population * building.Level
				end

				-- Energy production
				if config.EnergyProduction then
					energyProduction += config.EnergyProduction * building.Level
				end

				-- Water production
				if config.WaterProduction then
					waterProduction += config.WaterProduction * building.Level
				end

				-- Energy consumption
				if config.EnergyConsumption then
					energyConsumption += config.EnergyConsumption * building.Level
				end

				-- Water consumption
				if config.WaterConsumption then
					waterConsumption += config.WaterConsumption * building.Level
				end
			end
		end
	end

	-- Calculate net production (production - consumption)
	local netEnergyProduction = energyProduction - energyConsumption
	local netWaterProduction = waterProduction - waterConsumption

	-- Update player data with enhanced tracking
	DataManager.UpdatePlayerData(player, "Population", totalPopulation)
	DataManager.UpdatePlayerData(player, "EnergyProduction", energyProduction)
	DataManager.UpdatePlayerData(player, "WaterProduction", waterProduction)
	DataManager.UpdatePlayerData(player, "EnergyConsumption", energyConsumption)
	DataManager.UpdatePlayerData(player, "WaterConsumption", waterConsumption)
	DataManager.UpdatePlayerData(player, "NetEnergyProduction", netEnergyProduction)
	DataManager.UpdatePlayerData(player, "NetWaterProduction", netWaterProduction)

	-- Check for shortages and notify player
	if netEnergyProduction < 0 then
		RemoteEvents.ShowNotification:FireClient(player, "Warning",
			"⚡ Energy shortage! Build more power plants. Deficit: " .. math.abs(netEnergyProduction))
	end

	if netWaterProduction < 0 then
		RemoteEvents.ShowNotification:FireClient(player, "Warning",
			"💧 Water shortage! Build more water plants. Deficit: " .. math.abs(netWaterProduction))
	end

	print("🏙️ City Stats for", player.Name, "- Population:", totalPopulation,
		"Energy:", energyProduction .. "/" .. energyConsumption,
		"Water:", waterProduction .. "/" .. waterConsumption)
end

-- Handle building interaction
function BuildingManager.InteractWithBuilding(player, buildingId)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	local building = playerData.Buildings[buildingId]
	if not building then return end
	
	-- Open appropriate menu based on building type
	if building.Type == "CITY_HALL" then
		RemoteEvents.OpenBuildingMenu:FireClient(player, "CityHall", building)
	else
		RemoteEvents.OpenBuildingMenu:FireClient(player, "Building", building)
	end
end

-- Connect RemoteEvents directly from Assets folder
print("🔗 Connecting RemoteEvent handlers...")

-- StartBuildingPlacement event (NEW)
local startBuildingPlacementEvent = Assets:FindFirstChild("StartBuildingPlacement")
if startBuildingPlacementEvent then
	startBuildingPlacementEvent.OnServerEvent:Connect(function(player, buildingType)
		print("🏗️ Server received StartBuildingPlacement for:", buildingType, "from player:", player.Name)

		-- Validate building type
		local buildingConfig = Config.BUILDINGS[buildingType]
		if not buildingConfig then
			local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
			if showNotificationEvent then
				showNotificationEvent:FireClient(player, "Error", "Invalid building type: " .. tostring(buildingType))
			end
			return
		end

		-- Check if player can build this building
		local playerData = DataManager.GetPlayerData(player)
		if playerData then
			local canBuild, reason = BuildingSystem.CanBuild(buildingType, playerData)
			if not canBuild then
				local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
				if showNotificationEvent then
					showNotificationEvent:FireClient(player, "Error", reason)
				end
				return
			end
		end

		-- Send success notification to start placement mode
		local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
		if showNotificationEvent then
			showNotificationEvent:FireClient(player, "Info", "Click where you want to place the " .. buildingConfig.Name .. ". Press Q to cancel.")
		end

		print("✅ Building placement mode started for:", player.Name, "building:", buildingType)
	end)
	print("✅ StartBuildingPlacement event connected successfully")
else
	warn("❌ StartBuildingPlacement RemoteEvent not found!")
end

-- PlaceBuilding event
local placeBuildingEvent = Assets:FindFirstChild("PlaceBuilding")
if placeBuildingEvent then
	placeBuildingEvent.OnServerEvent:Connect(function(player, buildingType, position, rotation)
		local success, message = BuildingManager.PlaceBuilding(player, buildingType, position, rotation)
		if not success then
			local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
			if showNotificationEvent then
				showNotificationEvent:FireClient(player, "Error", message)
			end
		end
	end)
	print("✅ PlaceBuilding event connected successfully")
else
	warn("❌ PlaceBuilding RemoteEvent not found!")
end

-- UpgradeBuilding event
local upgradeBuildingEvent = Assets:FindFirstChild("UpgradeBuilding")
if upgradeBuildingEvent then
	upgradeBuildingEvent.OnServerEvent:Connect(function(player, buildingId)
		local success, message = BuildingManager.UpgradeBuilding(player, buildingId)
		if not success then
			local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
			if showNotificationEvent then
				showNotificationEvent:FireClient(player, "Error", message)
			end
		end
	end)
	print("✅ UpgradeBuilding event connected successfully")
else
	warn("❌ UpgradeBuilding RemoteEvent not found!")
end

-- StartBuildingRemoval event (NEW)
local startBuildingRemovalEvent = Assets:FindFirstChild("StartBuildingRemoval")
if startBuildingRemovalEvent then
	startBuildingRemovalEvent.OnServerEvent:Connect(function(player, buildingType)
		print("🗑️ Server received StartBuildingRemoval for:", buildingType, "from player:", player.Name)

		-- Initialize player state if not exists
		if not PlayerStates[player.UserId] then
			PlayerStates[player.UserId] = {}
		end

		-- Set player to removal mode
		PlayerStates[player.UserId].removalMode = true
		PlayerStates[player.UserId].selectedBuildingType = buildingType

		-- Send instruction notification to start removal mode
		local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
		if showNotificationEvent then
			showNotificationEvent:FireClient(player, "Info", "Click on a " .. buildingType .. " to remove it. Press Q to cancel.")
		end

		print("✅ Building removal mode started for:", player.Name, "building:", buildingType)
	end)
	print("✅ StartBuildingRemoval event connected successfully")
else
	warn("❌ StartBuildingRemoval RemoteEvent not found!")
end

-- RemoveBuilding event
local removeBuildingEvent = Assets:FindFirstChild("RemoveBuilding")
if removeBuildingEvent then
	removeBuildingEvent.OnServerEvent:Connect(function(player, buildingId)
		local success, message = BuildingManager.RemoveBuilding(player, buildingId)
		local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
		if showNotificationEvent then
			if success then
				showNotificationEvent:FireClient(player, "Success", message)
			else
				showNotificationEvent:FireClient(player, "Error", message)
			end
		end
	end)
	print("✅ RemoveBuilding event connected successfully")
else
	warn("❌ RemoveBuilding RemoteEvent not found!")
end

-- Setup RemoteFunction handlers with proper error handling
task.spawn(function()
	-- Wait a bit for RemoteFunctions to be fully loaded
	task.wait(1)

	-- Helper function to safely set up RemoteFunction handlers
	local function setupRemoteFunction(name, handler)
		local remoteFunction = RemoteFunctions[name]
		if remoteFunction then
			remoteFunction.OnServerInvoke = handler
			print("✅ Set up BuildingManager RemoteFunction handler:", name)
		else
			warn("❌ BuildingManager RemoteFunction not found:", name)
		end
	end

	-- Set up all RemoteFunction handlers
	setupRemoteFunction("CanPlaceBuilding", function(player, buildingType, position)
		local playerData = DataManager.GetPlayerData(player)
		if not playerData then return false end

		-- Get player's plot information
		local playerPlotInfo = PlotManager.GetPlayerPlotInfo(player)

		local gridPosition = BuildingSystem.WorldToGrid(position)
		return BuildingSystem.CanPlaceBuilding(buildingType, gridPosition, playerData.Buildings, playerData.Roads, playerPlotInfo)
	end)

	setupRemoteFunction("CanBuild", function(player, buildingType)
		local playerData = DataManager.GetPlayerData(player)
		if not playerData then
			return {canBuild = false, reason = "Player data not loaded"}
		end

		local canBuild, reason = BuildingSystem.CanBuild(buildingType, playerData)
		return {canBuild = canBuild, reason = reason}
	end)

	setupRemoteFunction("GetBuildingCost", function(_player, buildingType)
		local config = Config.BUILDINGS[buildingType]
		return config and config.Cost or nil
	end)

	setupRemoteFunction("GetUpgradeCost", function(player, buildingId)
		local playerData = DataManager.GetPlayerData(player)
		if not playerData then return nil end

		local building = playerData.Buildings[buildingId]
		if not building then return nil end

		return BuildingSystem.GetUpgradeCost(building.Type, building.Level)
	end)

	setupRemoteFunction("GetBuildingInfo", function(_player, buildingId)
		return BuildingManager.GetBuildingInfo(buildingId)
	end)

	setupRemoteFunction("DeleteBuildings", function(player, buildingIds)
		return BuildingManager.DeleteBuildings(player, buildingIds)
	end)

	print("✅ BuildingManager RemoteFunction handlers set up successfully")
end)

-- Clean up player state when they leave
Players.PlayerRemoving:Connect(function(player)
	PlayerStates[player.UserId] = nil
	print("🧹 Cleaned up building state for player:", player.Name)
end)

return BuildingManager
