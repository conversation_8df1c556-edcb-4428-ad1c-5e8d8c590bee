-- PlotManager.luau
-- Manages player plots, teleportation, and profile displays

local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))
local DataManager = require(script.Parent:WaitForChild("DataManager"))
local SoundEvents = require(script.Parent:WaitForChild("SoundEvents"))

local PlotManager = {}

-- Plot configuration
local PLOT_CONFIG = {
	PLOT_SIZE = Vector3.new(200, 1, 200), -- Size of each plot
	PLOT_SPACING = 50, -- Space between plots
	PLOTS_PER_ROW = 3, -- Number of plots per row
	TOTAL_PLOTS = 7, -- Total number of plots
	BASE_POSITION = Vector3.new(0, 0, 0), -- Starting position for plots
	SPAWN_HEIGHT = 10 -- Height above plot to spawn players
}

-- Plot data storage
local plotData = {}
local playerPlots = {} -- Maps player UserId to plot number

-- Initialize plot system
function PlotManager.Initialize()
	print("🏘️ Initializing Plot Manager...")
	
	-- Create plots folder if it doesn't exist
	local plotsFolder = Workspace:FindFirstChild("Plots")
	if not plotsFolder then
		plotsFolder = Instance.new("Folder")
		plotsFolder.Name = "Plots"
		plotsFolder.Parent = Workspace
	end
	
	-- Create all plots
	for i = 1, PLOT_CONFIG.TOTAL_PLOTS do
		PlotManager.CreatePlot(i)
	end
	
	-- Setup player events
	Players.PlayerAdded:Connect(PlotManager.OnPlayerJoined)
	Players.PlayerRemoving:Connect(PlotManager.OnPlayerLeft)
	
	print("✅ Plot Manager initialized with", PLOT_CONFIG.TOTAL_PLOTS, "plots!")
end

-- Create a single plot
function PlotManager.CreatePlot(plotNumber)
	local plotsFolder = Workspace:WaitForChild("Plots")
	
	-- Calculate plot position
	local row = math.floor((plotNumber - 1) / PLOT_CONFIG.PLOTS_PER_ROW)
	local col = (plotNumber - 1) % PLOT_CONFIG.PLOTS_PER_ROW
	
	local plotX = PLOT_CONFIG.BASE_POSITION.X + col * (PLOT_CONFIG.PLOT_SIZE.X + PLOT_CONFIG.PLOT_SPACING)
	local plotZ = PLOT_CONFIG.BASE_POSITION.Z + row * (PLOT_CONFIG.PLOT_SIZE.Z + PLOT_CONFIG.PLOT_SPACING)
	local plotPosition = Vector3.new(plotX, PLOT_CONFIG.BASE_POSITION.Y, plotZ)
	
	-- Create plot folder
	local plotFolder = Instance.new("Folder")
	plotFolder.Name = "Plot" .. plotNumber
	plotFolder.Parent = plotsFolder
	
	-- Create base part for building placement
	local basePart = Instance.new("Part")
	basePart.Name = "BasePart"
	basePart.Size = PLOT_CONFIG.PLOT_SIZE
	basePart.Position = plotPosition
	basePart.Anchored = true
	basePart.CanCollide = true
	basePart.Color = Color3.new(0.3, 0.7, 0.3) -- Green color
	basePart.Material = Enum.Material.Grass
	basePart.Parent = plotFolder
	
	-- Add plot border with collision enabled
	local border = Instance.new("Part")
	border.Name = "Border"
	border.Size = Vector3.new(PLOT_CONFIG.PLOT_SIZE.X + 4, 2, PLOT_CONFIG.PLOT_SIZE.Z + 4)
	border.Position = plotPosition + Vector3.new(0, 1, 0)
	border.Anchored = true
	border.CanCollide = true -- Enable collision for borders
	border.Color = Color3.new(0.6, 0.4, 0.2) -- Brown border
	border.Material = Enum.Material.Wood
	border.Transparency = 0.5
	border.Parent = plotFolder
	
	-- Create spawn point
	local spawnPoint = Instance.new("Part")
	spawnPoint.Name = "SpawnPoint"
	spawnPoint.Size = Vector3.new(4, 1, 4)
	spawnPoint.Position = plotPosition + Vector3.new(0, PLOT_CONFIG.SPAWN_HEIGHT, 0)
	spawnPoint.Anchored = true
	spawnPoint.CanCollide = false
	spawnPoint.Color = Color3.new(0.2, 0.8, 1) -- Light blue
	spawnPoint.Material = Enum.Material.Neon
	spawnPoint.Transparency = 0.7
	spawnPoint.Parent = plotFolder
	
	-- Create plot sign
	local sign = Instance.new("Part")
	sign.Name = "PlotSign"
	sign.Size = Vector3.new(8, 6, 1)
	sign.Position = plotPosition + Vector3.new(0, 4, -PLOT_CONFIG.PLOT_SIZE.Z/2 - 5)
	sign.Anchored = true
	sign.CanCollide = false
	sign.Color = Color3.new(0.8, 0.8, 0.8)
	sign.Material = Enum.Material.Plastic
	sign.Parent = plotFolder
	
	-- Create BillboardGui for plot info
	local billboardGui = Instance.new("BillboardGui")
	billboardGui.Name = "PlotInfo"
	billboardGui.Size = UDim2.new(0, 200, 0, 150)
	billboardGui.StudsOffset = Vector3.new(0, 3, 0)
	billboardGui.Parent = sign
	
	-- Plot title
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Name = "Title"
	titleLabel.Size = UDim2.new(1, 0, 0.3, 0)
	titleLabel.Position = UDim2.new(0, 0, 0, 0)
	titleLabel.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	titleLabel.BackgroundTransparency = 0.3
	titleLabel.Text = "🏘️ Plot " .. plotNumber
	titleLabel.TextColor3 = Color3.new(1, 1, 1)
	titleLabel.TextScaled = true
	titleLabel.Font = Enum.Font.SourceSansBold
	titleLabel.Parent = billboardGui
	
	-- Owner label with profile image
	local ownerFrame = Instance.new("Frame")
	ownerFrame.Name = "OwnerFrame"
	ownerFrame.Size = UDim2.new(1, 0, 0.25, 0)
	ownerFrame.Position = UDim2.new(0, 0, 0.3, 0)
	ownerFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
	ownerFrame.BackgroundTransparency = 0.3
	ownerFrame.Parent = billboardGui

	-- Player profile image
	local profileImage = Instance.new("ImageLabel")
	profileImage.Name = "ProfileImage"
	profileImage.Size = UDim2.new(0, 30, 0, 30)
	profileImage.Position = UDim2.new(0, 5, 0.5, -15)
	profileImage.BackgroundTransparency = 1
	profileImage.Image = "rbxasset://textures/ui/GuiImagePlaceholder.png" -- Default placeholder
	profileImage.Parent = ownerFrame

	-- Owner text label
	local ownerLabel = Instance.new("TextLabel")
	ownerLabel.Name = "Owner"
	ownerLabel.Size = UDim2.new(1, -40, 1, 0)
	ownerLabel.Position = UDim2.new(0, 40, 0, 0)
	ownerLabel.BackgroundTransparency = 1
	ownerLabel.Text = "👤 Available"
	ownerLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	ownerLabel.TextScaled = true
	ownerLabel.Font = Enum.Font.SourceSans
	ownerLabel.Parent = ownerFrame
	
	-- Location label
	local locationLabel = Instance.new("TextLabel")
	locationLabel.Name = "Location"
	locationLabel.Size = UDim2.new(1, 0, 0.25, 0)
	locationLabel.Position = UDim2.new(0, 0, 0.55, 0)
	locationLabel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
	locationLabel.BackgroundTransparency = 0.3
	locationLabel.Text = "📍 " .. math.floor(plotPosition.X) .. ", " .. math.floor(plotPosition.Z)
	locationLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	locationLabel.TextScaled = true
	locationLabel.Font = Enum.Font.SourceSans
	locationLabel.Parent = billboardGui
	
	-- Stats label
	local statsLabel = Instance.new("TextLabel")
	statsLabel.Name = "Stats"
	statsLabel.Size = UDim2.new(1, 0, 0.2, 0)
	statsLabel.Position = UDim2.new(0, 0, 0.8, 0)
	statsLabel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
	statsLabel.BackgroundTransparency = 0.3
	statsLabel.Text = "🏗️ 0 Buildings"
	statsLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	statsLabel.TextScaled = true
	statsLabel.Font = Enum.Font.SourceSans
	statsLabel.Parent = billboardGui
	
	-- Add corner radius to all labels
	for _, label in ipairs({titleLabel, ownerLabel, locationLabel, statsLabel}) do
		local corner = Instance.new("UICorner")
		corner.CornerRadius = UDim.new(0, 8)
		corner.Parent = label
	end

	-- Add ClickDetector for plot claiming
	local clickDetector = Instance.new("ClickDetector")
	clickDetector.Name = "PlotClickDetector"
	clickDetector.MaxActivationDistance = 50
	clickDetector.Parent = sign

	-- Handle plot sign clicks
	clickDetector.MouseClick:Connect(function(player)
		PlotManager.HandlePlotSignClick(player, plotNumber)
	end)
	
	-- Store plot data
	plotData[plotNumber] = {
		PlotNumber = plotNumber,
		Position = plotPosition,
		Owner = nil,
		OwnerName = nil,
		Buildings = {},
		CreatedAt = tick(),
		LastActive = tick()
	}
	
	print("🏘️ Created Plot", plotNumber, "at position", plotPosition)
end

-- Assign plot to player
function PlotManager.AssignPlot(player)
	-- Check if player already has a plot
	if playerPlots[player.UserId] then
		return playerPlots[player.UserId]
	end
	
	-- Find available plot
	for plotNumber = 1, PLOT_CONFIG.TOTAL_PLOTS do
		if not plotData[plotNumber].Owner then
			-- Assign plot to player
			plotData[plotNumber].Owner = player.UserId
			plotData[plotNumber].OwnerName = player.Name
			plotData[plotNumber].LastActive = tick()
			
			playerPlots[player.UserId] = plotNumber
			
			-- Update plot display
			PlotManager.UpdatePlotDisplay(plotNumber)
			
			print("🏘️ Assigned Plot", plotNumber, "to", player.Name)
			return plotNumber
		end
	end
	
	-- No available plots
	warn("⚠️ No available plots for", player.Name)
	return nil
end

-- Update plot display
function PlotManager.UpdatePlotDisplay(plotNumber)
	local plot = plotData[plotNumber]
	if not plot then return end
	
	local plotFolder = Workspace.Plots:FindFirstChild("Plot" .. plotNumber)
	if not plotFolder then return end
	
	local sign = plotFolder:FindFirstChild("PlotSign")
	if not sign then return end
	
	local billboardGui = sign:FindFirstChild("PlotInfo")
	if not billboardGui then return end
	
	local ownerFrame = billboardGui:FindFirstChild("OwnerFrame")
	local ownerLabel = ownerFrame and ownerFrame:FindFirstChild("Owner")
	local profileImage = ownerFrame and ownerFrame:FindFirstChild("ProfileImage")
	local statsLabel = billboardGui:FindFirstChild("Stats")

	if plot.Owner then
		-- Plot is owned
		if ownerLabel then
			ownerLabel.Text = (plot.OwnerName or "Unknown")
			ownerLabel.TextColor3 = Color3.new(0.2, 0.8, 0.2) -- Green
		end

		-- Update profile image
		if profileImage then
			local success, userId = pcall(function()
				return Players:GetUserIdFromNameAsync(plot.OwnerName or "")
			end)

			if success and userId then
				profileImage.Image = "https://www.roblox.com/headshot-thumbnail/image?userId=" .. userId .. "&width=150&height=150&format=png"
			else
				profileImage.Image = "rbxasset://textures/ui/GuiImagePlaceholder.png"
			end
		end

		-- Count buildings
		local buildingCount = 0
		for _ in pairs(plot.Buildings) do
			buildingCount = buildingCount + 1
		end

		statsLabel.Text = "🏗️ " .. buildingCount .. " Buildings"
		statsLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)

		-- Change plot color to show ownership
		local basePart = plotFolder:FindFirstChild("BasePart")
		if basePart then
			basePart.Color = Color3.new(0.2, 0.6, 0.8) -- Blue for owned
		end

		-- Make PlotSign more visible when claimed
		if sign then
			sign.Transparency = 0.2 -- More visible when owned
		end
	else
		-- Plot is available
		if ownerLabel then
			ownerLabel.Text = "👤 Available"
			ownerLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8) -- Gray
		end

		-- Reset profile image
		if profileImage then
			profileImage.Image = "rbxasset://textures/ui/GuiImagePlaceholder.png"
		end

		statsLabel.Text = "🏗️ 0 Buildings"
		statsLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)

		-- Reset plot color
		local basePart = plotFolder:FindFirstChild("BasePart")
		if basePart then
			basePart.Color = Color3.new(0.3, 0.7, 0.3) -- Green for available
		end

		-- Make PlotSign less visible when available
		if sign then
			sign.Transparency = 0.8 -- Less visible when available
		end
	end
end

-- Teleport player to their plot
function PlotManager.TeleportToPlot(player, plotNumber)
	plotNumber = plotNumber or playerPlots[player.UserId]
	
	if not plotNumber or not plotData[plotNumber] then
		RemoteEvents.ShowNotification:FireClient(player, "Error", "Plot not found!")
		return false
	end
	
	local plotFolder = Workspace.Plots:FindFirstChild("Plot" .. plotNumber)
	if not plotFolder then
		RemoteEvents.ShowNotification:FireClient(player, "Error", "Plot folder not found!")
		return false
	end
	
	local spawnPoint = plotFolder:FindFirstChild("SpawnPoint")
	if not spawnPoint then
		RemoteEvents.ShowNotification:FireClient(player, "Error", "Spawn point not found!")
		return false
	end
	
	-- Teleport player
	if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
		player.Character.HumanoidRootPart.CFrame = CFrame.new(spawnPoint.Position + Vector3.new(0, 5, 0))
		
		-- Update last active time
		plotData[plotNumber].LastActive = tick()
		
		RemoteEvents.ShowNotification:FireClient(player, "Success", "🏘️ Teleported to Plot " .. plotNumber .. "!")
		print("🏘️ Teleported", player.Name, "to Plot", plotNumber)
		return true
	else
		RemoteEvents.ShowNotification:FireClient(player, "Error", "Character not found!")
		return false
	end
end

-- Handle player joining
function PlotManager.OnPlayerJoined(player)
	-- Wait for character to spawn
	player.CharacterAdded:Connect(function(character)
		task.wait(2) -- Wait for character to fully load

		-- Check if player already has a plot from previous session
		local existingPlotNumber = playerPlots[player.UserId]
		if existingPlotNumber and plotData[existingPlotNumber] and plotData[existingPlotNumber].Owner == player.UserId then
			-- Player has an existing plot, restore buildings and teleport
			task.wait(1)
			PlotManager.RestorePlayerBuildings(player, existingPlotNumber)
			task.wait(0.5) -- Small delay to ensure buildings are restored
			PlotManager.TeleportToPlot(player, existingPlotNumber)
			RemoteEvents.ShowNotification:FireClient(player, "Info", "🏘️ Welcome back! Teleported to your Plot " .. existingPlotNumber .. ".")
		else
			-- Player doesn't have a plot, show welcome message
			RemoteEvents.ShowNotification:FireClient(player, "Info", "🏘️ Welcome to UrbanSim! Click on any green plot sign to claim your plot, or use the Plot UI to browse available plots.")
		end
	end)

	-- If player already has character, handle immediately
	if player.Character then
		task.spawn(function()
			task.wait(3)
			local existingPlotNumber = playerPlots[player.UserId]
			if existingPlotNumber and plotData[existingPlotNumber] and plotData[existingPlotNumber].Owner == player.UserId then
				PlotManager.RestorePlayerBuildings(player, existingPlotNumber)
				task.wait(0.5) -- Small delay to ensure buildings are restored
				PlotManager.TeleportToPlot(player, existingPlotNumber)
				RemoteEvents.ShowNotification:FireClient(player, "Info", "🏘️ Welcome back! Teleported to your Plot " .. existingPlotNumber .. ".")
			else
				RemoteEvents.ShowNotification:FireClient(player, "Info", "🏘️ Welcome to UrbanSim! Click on any green plot sign to claim your plot, or use the Plot UI to browse available plots.")
			end
		end)
	end
end

-- Handle plot sign clicks
function PlotManager.HandlePlotSignClick(player, plotNumber)
	local plot = plotData[plotNumber]
	if not plot then
		RemoteEvents.ShowNotification:FireClient(player, "Error", "Plot not found!")
		return
	end

	-- Check if plot is available
	if not plot.Owner then
		-- Plot is available - offer to claim it
		local playerCurrentPlot = playerPlots[player.UserId]

		if playerCurrentPlot then
			-- Player already has a plot
			RemoteEvents.ShowNotification:FireClient(player, "Info", "You already own Plot " .. playerCurrentPlot .. ". Release it first to claim this plot.")
		else
			-- Player doesn't have a plot - claim it
			local success, message = PlotManager.ClaimPlot(player, plotNumber)
			if success then
				RemoteEvents.ShowNotification:FireClient(player, "Success", message)
				-- Teleport to the newly claimed plot
				task.wait(1)
				PlotManager.TeleportToPlot(player, plotNumber)
			else
				RemoteEvents.ShowNotification:FireClient(player, "Error", message)
			end
		end
	else
		-- Plot is owned
		if plot.Owner == player.UserId then
			-- Player owns this plot - teleport to it
			PlotManager.TeleportToPlot(player, plotNumber)
		else
			-- Plot is owned by someone else
			RemoteEvents.ShowNotification:FireClient(player, "Info", "This plot is owned by " .. (plot.OwnerName or "someone else") .. ".")
		end
	end
end

-- Handle player leaving with enhanced cleanup
function PlotManager.OnPlayerLeft(player)
	local plotNumber = playerPlots[player.UserId]

	if plotNumber and plotData[plotNumber] then
		-- Mark as inactive
		plotData[plotNumber].LastActive = tick()

		-- Check if plot has buildings
		local buildingCount = 0
		for _ in pairs(plotData[plotNumber].Buildings) do
			buildingCount = buildingCount + 1
		end

		if buildingCount == 0 then
			-- If no buildings, release plot immediately for new players
			print("🏘️ Player", player.Name, "left with empty plot. Releasing Plot", plotNumber, "immediately.")

			-- Release the plot
			plotData[plotNumber].Owner = nil
			plotData[plotNumber].OwnerName = nil
			plotData[plotNumber].ReleasedAt = tick()

			-- Remove from player mapping
			playerPlots[player.UserId] = nil

			-- Update plot display
			PlotManager.UpdatePlotDisplay(plotNumber)

			-- Notify all players
			RemoteEvents.PlotReleased:FireAllClients(plotNumber, player.Name .. " (Left game)")
		else
			print("🏘️ Player", player.Name, "left. Plot", plotNumber, "has", buildingCount, "buildings - keeping ownership.")
			-- Keep plot ownership but mark as inactive for auto-release later
		end
	end

	-- Clean up player plot mapping only if plot was released
	if not plotNumber or not plotData[plotNumber] or not plotData[plotNumber].Owner then
		playerPlots[player.UserId] = nil
	end
end

-- Check if position is within player's plot boundaries
function PlotManager.IsPositionInPlayerPlot(player, position)
	local plotNumber = playerPlots[player.UserId]
	if not plotNumber or not plotData[plotNumber] then
		return false
	end

	local plot = plotData[plotNumber]
	local plotPos = plot.Position
	local halfSize = PLOT_CONFIG.PLOT_SIZE / 2

	-- Check if position is within plot boundaries
	local withinX = position.X >= (plotPos.X - halfSize.X) and position.X <= (plotPos.X + halfSize.X)
	local withinZ = position.Z >= (plotPos.Z - halfSize.Z) and position.Z <= (plotPos.Z + halfSize.Z)

	return withinX and withinZ
end



-- Get plot info for player
function PlotManager.GetPlayerPlotInfo(player)
	local plotNumber = playerPlots[player.UserId]
	print("🔍 GetPlayerPlotInfo for", player.Name, "- Plot number:", plotNumber)

	if not plotNumber or not plotData[plotNumber] then
		print("❌ No plot found for player", player.Name)
		return nil
	end

	local plot = plotData[plotNumber]
	local buildingCount = 0
	for _ in pairs(plot.Buildings) do
		buildingCount = buildingCount + 1
	end

	local plotInfo = {
		PlotNumber = plotNumber,
		Position = plot.Position,
		Buildings = buildingCount,
		LastActive = plot.LastActive
	}

	print("✅ Plot info for", player.Name, ":", plotInfo.PlotNumber, "at", plotInfo.Position)
	return plotInfo
end

-- Add building to plot
function PlotManager.AddBuildingToPlot(player, buildingId, buildingType, position)
	local plotNumber = playerPlots[player.UserId]

	if not plotNumber or not plotData[plotNumber] then
		return false, "Player has no assigned plot"
	end

	-- Add building to plot data
	plotData[plotNumber].Buildings[buildingId] = {
		Type = buildingType,
		Position = position,
		AddedAt = tick()
	}

	-- Update plot display
	PlotManager.UpdatePlotDisplay(plotNumber)

	print("🏗️ Added building", buildingType, "to Plot", plotNumber)
	return true
end

-- Remove building from plot
function PlotManager.RemoveBuildingFromPlot(player, buildingId)
	local plotNumber = playerPlots[player.UserId]

	if not plotNumber or not plotData[plotNumber] then
		return false, "Player has no assigned plot"
	end

	-- Remove building from plot data
	if plotData[plotNumber].Buildings[buildingId] then
		plotData[plotNumber].Buildings[buildingId] = nil

		-- Update plot display
		PlotManager.UpdatePlotDisplay(plotNumber)

		print("🗑️ Removed building from Plot", plotNumber)
		return true
	end

	return false, "Building not found on plot"
end

-- Claim a specific plot
function PlotManager.ClaimPlot(player, plotNumber)
	-- Validate plot number
	if not plotNumber or plotNumber < 1 or plotNumber > PLOT_CONFIG.TOTAL_PLOTS then
		return false, "Invalid plot number"
	end

	-- Check if player already has a plot
	if playerPlots[player.UserId] then
		return false, "You already own Plot " .. playerPlots[player.UserId]
	end

	-- Check if plot is available
	if plotData[plotNumber].Owner then
		return false, "Plot " .. plotNumber .. " is already owned by " .. (plotData[plotNumber].OwnerName or "someone")
	end

	-- Claim the plot
	plotData[plotNumber].Owner = player.UserId
	plotData[plotNumber].OwnerName = player.Name
	plotData[plotNumber].LastActive = tick()
	plotData[plotNumber].ClaimedAt = tick()

	playerPlots[player.UserId] = plotNumber

	-- Update plot display
	PlotManager.UpdatePlotDisplay(plotNumber)

	-- Restore player's buildings from DataStore
	PlotManager.RestorePlayerBuildings(player, plotNumber)

	-- Notify all players
	RemoteEvents.PlotClaimed:FireAllClients(plotNumber, player.Name)

	-- Trigger sound event
	SoundEvents.OnPlotClaimed(player, plotNumber)

	print("🏘️ Player", player.Name, "claimed Plot", plotNumber)
	return true, "Successfully claimed Plot " .. plotNumber .. "!"
end

-- Release a plot
function PlotManager.ReleasePlot(player)
	local plotNumber = playerPlots[player.UserId]

	if not plotNumber then
		return false, "You don't own any plot"
	end

	-- Check if plot has buildings
	local buildingCount = 0
	for _ in pairs(plotData[plotNumber].Buildings) do
		buildingCount = buildingCount + 1
	end

	if buildingCount > 0 then
		return false, "Cannot release plot with " .. buildingCount .. " buildings. Remove all buildings first."
	end

	-- Release the plot
	local plotOwnerName = plotData[plotNumber].OwnerName
	plotData[plotNumber].Owner = nil
	plotData[plotNumber].OwnerName = nil
	plotData[plotNumber].LastActive = tick()
	plotData[plotNumber].ReleasedAt = tick()

	playerPlots[player.UserId] = nil

	-- Update plot display
	PlotManager.UpdatePlotDisplay(plotNumber)

	-- Notify all players
	RemoteEvents.PlotReleased:FireAllClients(plotNumber, plotOwnerName)

	-- Trigger sound event
	SoundEvents.OnPlotReleased(player, plotNumber)

	print("🏘️ Player", player.Name, "released Plot", plotNumber)
	return true, "Successfully released Plot " .. plotNumber .. "!"
end

-- Get specific plot info
function PlotManager.GetPlotInfo(plotNumber)
	if not plotNumber or not plotData[plotNumber] then
		return nil
	end

	local plot = plotData[plotNumber]
	local buildingCount = 0
	for _ in pairs(plot.Buildings) do
		buildingCount = buildingCount + 1
	end

	return {
		PlotNumber = plotNumber,
		Position = plot.Position,
		Owner = plot.Owner,
		OwnerName = plot.OwnerName,
		Buildings = buildingCount,
		LastActive = plot.LastActive,
		CreatedAt = plot.CreatedAt,
		ClaimedAt = plot.ClaimedAt,
		ReleasedAt = plot.ReleasedAt,
		IsAvailable = not plot.Owner
	}
end

-- Get all plots info (for plot browser)
function PlotManager.GetAllPlotsInfo()
	local plotsInfo = {}

	for plotNumber = 1, PLOT_CONFIG.TOTAL_PLOTS do
		plotsInfo[plotNumber] = PlotManager.GetPlotInfo(plotNumber)
	end

	return plotsInfo
end

-- Auto-release inactive plots (optional - can be called periodically)
function PlotManager.AutoReleaseInactivePlots(inactiveThresholdHours)
	inactiveThresholdHours = inactiveThresholdHours or 24 -- Default 24 hours
	local currentTime = tick()
	local thresholdTime = inactiveThresholdHours * 3600 -- Convert to seconds

	for plotNumber, plot in pairs(plotData) do
		if plot.Owner and plot.LastActive then
			local inactiveTime = currentTime - plot.LastActive

			if inactiveTime > thresholdTime then
				-- Check if plot has buildings
				local buildingCount = 0
				for _ in pairs(plot.Buildings) do
					buildingCount = buildingCount + 1
				end

				-- Only auto-release if no buildings
				if buildingCount == 0 then
					local ownerName = plot.OwnerName
					plot.Owner = nil
					plot.OwnerName = nil
					plot.LastActive = currentTime
					plot.ReleasedAt = currentTime

					-- Remove from player mapping
					for userId, pNum in pairs(playerPlots) do
						if pNum == plotNumber then
							playerPlots[userId] = nil
							break
						end
					end

					-- Update plot display
					PlotManager.UpdatePlotDisplay(plotNumber)

					-- Notify all players
					RemoteEvents.PlotReleased:FireAllClients(plotNumber, ownerName .. " (Auto-released)")

					print("🏘️ Auto-released Plot", plotNumber, "from inactive player", ownerName)
				end
			end
		end
	end
end

-- Get all plots data (for admin/debugging)
function PlotManager.GetAllPlotsData()
	return plotData
end

-- Get player plots mapping (for admin/debugging)
function PlotManager.GetPlayerPlotsMapping()
	return playerPlots
end

-- Rename plot function
function PlotManager.RenamePlot(player, newName)
	local plotNumber = playerPlots[player.UserId]

	if not plotNumber or not plotData[plotNumber] then
		return false, "You don't own any plot"
	end

	if not plotData[plotNumber].Owner or plotData[plotNumber].Owner ~= player.UserId then
		return false, "You don't own this plot"
	end

	-- Validate name
	if not newName or newName == "" or #newName > 30 then
		return false, "Plot name must be 1-30 characters"
	end

	-- Filter inappropriate content (basic filter)
	local filteredName = newName:gsub("[^%w%s%-_]", "")
	if filteredName ~= newName then
		return false, "Plot name contains invalid characters"
	end

	-- Update plot name
	plotData[plotNumber].CustomName = filteredName

	-- Update plot display
	local plotFolder = Workspace.Plots:FindFirstChild("Plot" .. plotNumber)
	if plotFolder then
		local sign = plotFolder:FindFirstChild("PlotSign")
		if sign then
			local billboardGui = sign:FindFirstChild("PlotInfo")
			if billboardGui then
				local titleLabel = billboardGui:FindFirstChild("Title")
				if titleLabel then
					titleLabel.Text = "🏘️ " .. filteredName
				end
			end
		end
	end

	print("🏘️ Player", player.Name, "renamed Plot", plotNumber, "to:", filteredName)
	return true, "Successfully renamed plot to: " .. filteredName
end

-- Change plot border color and material
function PlotManager.CustomizePlotBorder(player, color, material)
	local plotNumber = playerPlots[player.UserId]

	if not plotNumber or not plotData[plotNumber] then
		return false, "You don't own any plot"
	end

	if not plotData[plotNumber].Owner or plotData[plotNumber].Owner ~= player.UserId then
		return false, "You don't own this plot"
	end

	-- Validate color (RGB values 0-1)
	if not color or type(color) ~= "table" or not color.R or not color.G or not color.B then
		return false, "Invalid color format"
	end

	-- Validate material
	local validMaterials = {
		"Wood", "Plastic", "Metal", "Concrete", "Brick", "Marble",
		"Granite", "Glass", "Neon", "SmoothPlastic"
	}

	local isValidMaterial = false
	for _, validMaterial in ipairs(validMaterials) do
		if material == validMaterial then
			isValidMaterial = true
			break
		end
	end

	if not isValidMaterial then
		return false, "Invalid material. Valid materials: " .. table.concat(validMaterials, ", ")
	end

	-- Update border
	local plotFolder = Workspace.Plots:FindFirstChild("Plot" .. plotNumber)
	if plotFolder then
		local border = plotFolder:FindFirstChild("Border")
		if border then
			border.Color = Color3.new(color.R, color.G, color.B)
			border.Material = Enum.Material[material]

			-- Store customization in plot data
			plotData[plotNumber].BorderColor = color
			plotData[plotNumber].BorderMaterial = material
		end
	end

	print("🎨 Player", player.Name, "customized Plot", plotNumber, "border - Color:", color, "Material:", material)
	return true, "Successfully customized plot border!"
end

-- Restore player's buildings from DataStore when claiming a plot
function PlotManager.RestorePlayerBuildings(player, plotNumber)
	-- Get BuildingManager (DataManager already available at module level)
	local BuildingManager = require(script.Parent:WaitForChild("BuildingManager"))

	-- Get player data from DataStore
	local playerData = DataManager.GetPlayerData(player)
	if not playerData or not playerData.Buildings then
		print("🏘️ No buildings to restore for player", player.Name)
		return
	end

	local restoredCount = 0
	local plotPosition = plotData[plotNumber].Position

	-- Get plot boundaries for validation
	local plotMinX = plotPosition.X - PLOT_CONFIG.PLOT_SIZE.X/2
	local plotMaxX = plotPosition.X + PLOT_CONFIG.PLOT_SIZE.X/2
	local plotMinZ = plotPosition.Z - PLOT_CONFIG.PLOT_SIZE.Z/2
	local plotMaxZ = plotPosition.Z + PLOT_CONFIG.PLOT_SIZE.Z/2

	-- Restore each building that belongs on this plot
	for buildingId, buildingData in pairs(playerData.Buildings) do
		if buildingData.Position and buildingData.Owner == player.UserId then
			local buildingPos = buildingData.Position

			-- Check if building is within plot boundaries
			if buildingPos.X >= plotMinX and buildingPos.X <= plotMaxX and
			   buildingPos.Z >= plotMinZ and buildingPos.Z <= plotMaxZ then

				-- Create the physical building model
				local success, error = pcall(function()
					BuildingManager.CreateBuildingModel(buildingData)
				end)

				if success then
					-- Add building to plot data
					plotData[plotNumber].Buildings[buildingId] = {
						Type = buildingData.Type,
						Position = buildingData.Position,
						AddedAt = buildingData.PlacedAt or tick()
					}

					restoredCount = restoredCount + 1
					print("🏗️ Restored building", buildingData.Type, "for player", player.Name)
				else
					warn("❌ Failed to restore building", buildingId, "for player", player.Name, ":", error)
				end
			end
		end
	end

	-- Update plot display with new building count
	PlotManager.UpdatePlotDisplay(plotNumber)

	-- Notify player about restored buildings
	if restoredCount > 0 then
		RemoteEvents.ShowNotification:FireClient(player, "Success",
			"🏗️ Restored " .. restoredCount .. " buildings from your previous session!")
		print("🏘️ Restored", restoredCount, "buildings for player", player.Name, "on Plot", plotNumber)
	end
end

-- Setup RemoteFunction handlers
task.spawn(function()
	-- Wait for RemoteFunctions to be created
	local Assets = ReplicatedStorage:WaitForChild("Assets")
	local RemoteFunctions = require(Assets:WaitForChild("RemoteFunctions"))

	-- Get player plot info
	RemoteFunctions.GetPlayerPlotInfo.OnServerInvoke = function(player)
		return PlotManager.GetPlayerPlotInfo(player)
	end

	-- Get all plots info
	RemoteFunctions.GetAllPlotsInfo.OnServerInvoke = function(_player)
		return PlotManager.GetAllPlotsInfo()
	end

	-- Claim plot
	RemoteFunctions.ClaimPlot.OnServerInvoke = function(player, plotNumber)
		return PlotManager.ClaimPlot(player, plotNumber)
	end

	-- Release plot
	RemoteFunctions.ReleasePlot.OnServerInvoke = function(player)
		return PlotManager.ReleasePlot(player)
	end

	-- Teleport to plot
	RemoteFunctions.TeleportToPlot.OnServerInvoke = function(player, plotNumber)
		return PlotManager.TeleportToPlot(player, plotNumber)
	end

	-- Rename plot
	RemoteFunctions.RenamePlot.OnServerInvoke = function(player, newName)
		return PlotManager.RenamePlot(player, newName)
	end

	-- Customize plot border
	RemoteFunctions.CustomizePlotBorder.OnServerInvoke = function(player, color, material)
		return PlotManager.CustomizePlotBorder(player, color, material)
	end

	-- Get specific plot info
	RemoteFunctions.GetPlotInfo.OnServerInvoke = function(_player, plotNumber)
		return PlotManager.GetPlotInfo(plotNumber)
	end

	print("✅ PlotManager RemoteFunction handlers set up successfully")
end)

return PlotManager
