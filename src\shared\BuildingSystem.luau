--[[
	Building System Module
	Shared logic for building placement, validation, and management
]]

local Config = require(script.Parent.Config)

local BuildingSystem = {}

-- Grid utilities with SimCity-style snapping
function BuildingSystem.WorldToGrid(worldPosition)
	-- Enhanced grid snapping for better alignment
	local gridX = math.floor(worldPosition.X / Config.GRID_SIZE + 0.5)
	local gridZ = math.floor(worldPosition.Z / Config.GRID_SIZE + 0.5)
	return Vector2.new(gridX, gridZ)
end

function BuildingSystem.GridToWorld(gridPosition)
	-- Center buildings perfectly on grid
	local worldX = gridPosition.X * Config.GRID_SIZE
	local worldZ = gridPosition.Y * Config.GRID_SIZE
	return Vector3.new(worldX, 0, worldZ)
end

-- Snap world position to nearest grid point (SimCity-style)
function BuildingSystem.SnapToGrid(worldPosition)
	local gridPos = BuildingSystem.WorldToGrid(worldPosition)
	return BuildingSystem.GridToWorld(gridPos)
end

-- Get building footprint on grid (for multi-tile buildings)
function BuildingSystem.GetBuildingFootprint(buildingType, gridPosition, rotation)
	local buildingConfig = Config.BUILDINGS[buildingType]
	if not buildingConfig or not buildingConfig.Size then
		return {gridPosition}
	end

	local size = buildingConfig.Size
	local width = math.ceil(size[1] / Config.GRID_SIZE)
	local depth = math.ceil(size[3] / Config.GRID_SIZE)

	-- Handle rotation (90-degree increments)
	if rotation == 90 or rotation == 270 then
		width, depth = depth, width
	end

	local footprint = {}
	for x = 0, width - 1 do
		for z = 0, depth - 1 do
			table.insert(footprint, Vector2.new(gridPosition.X + x, gridPosition.Y + z))
		end
	end

	return footprint
end

-- Building placement validation (with plot support)
function BuildingSystem.CanPlaceBuilding(buildingType, gridPosition, existingBuildings, roads, playerPlotInfo)
	print("🔍 CanPlaceBuilding called with:", buildingType, gridPosition, "plotInfo:", playerPlotInfo and "YES" or "NO")

	-- Validate inputs
	if not buildingType or type(buildingType) ~= "string" then
		print("❌ Invalid building type:", buildingType)
		return false, "Invalid building type"
	end

	if not gridPosition or type(gridPosition) ~= "userdata" then
		print("❌ Invalid grid position:", gridPosition)
		return false, "Invalid grid position"
	end

	-- Validate gridPosition has required properties (Vector2 has X and Y)
	if not gridPosition.X or not gridPosition.Y then
		print("❌ Grid position missing coordinates:", gridPosition)
		return false, "Grid position missing coordinates"
	end

	local buildingConfig = Config.BUILDINGS[buildingType]
	if not buildingConfig then
		print("❌ Building config not found for:", buildingType)
		return false, "Invalid building type"
	end

	-- Convert grid position to world position for plot checking
	local worldPosition = BuildingSystem.GridToWorld(gridPosition)
	print("🌍 World position:", worldPosition)

	-- Check if position is within player's plot (if plot system is enabled)
	if playerPlotInfo then
		print("🏘️ Checking plot boundaries for plot:", playerPlotInfo.PlotNumber, "at position:", playerPlotInfo.Position)
		local isWithinPlot, plotReason = BuildingSystem.IsWithinPlayerPlot(worldPosition, playerPlotInfo)
		print("🏘️ Plot check result:", isWithinPlot, plotReason)
		if not isWithinPlot then
			return false, plotReason or "Must build within your assigned plot"
		end
	else
		print("🌍 No plot info, checking city bounds")
		-- Fallback to city bounds check if no plot system
		if not BuildingSystem.IsWithinCityBounds(gridPosition) then
			print("❌ Outside city bounds")
			return false, "Outside city bounds"
		end
	end
	
	-- Check for overlapping buildings using footprint system
	local footprint = BuildingSystem.GetBuildingFootprint(buildingType, gridPosition, 0) -- Default rotation
	for _, pos in ipairs(footprint) do
		local posKey = tostring(pos)
		if existingBuildings[posKey] then
			return false, "Position occupied"
		end

		-- Check if position is within city bounds
		if pos.X < -Config.CITY_SIZE/2 or pos.X > Config.CITY_SIZE/2 or
		   pos.Y < -Config.CITY_SIZE/2 or pos.Y > Config.CITY_SIZE/2 then
			return false, "Outside city bounds"
		end
	end
	
	-- Check road connectivity (except for roads themselves)
	-- Skip road connectivity check if building on a plot
	if buildingType ~= "ROAD" and not playerPlotInfo then
		if not BuildingSystem.IsConnectedToRoad(gridPosition, buildingConfig.Size, roads) then
			return false, "Must be connected to road"
		end
	end
	
	return true, "Valid placement"
end

-- Check if building is connected to road network
function BuildingSystem.IsConnectedToRoad(gridPosition, buildingSize, roads)
	-- Validate inputs
	if not gridPosition or not gridPosition.X or not gridPosition.Y then
		return false -- Invalid position, assume not connected
	end

	if not buildingSize or not buildingSize[1] or not buildingSize[3] then
		return false -- Invalid building size
	end

	local gridSizeX = math.ceil(buildingSize[1] / Config.GRID_SIZE)
	local gridSizeZ = math.ceil(buildingSize[3] / Config.GRID_SIZE)
	
	-- Check all adjacent positions for roads
	for x = gridPosition.X - 1, gridPosition.X + gridSizeX do
		for z = gridPosition.Y - 1, gridPosition.Y + gridSizeZ do
			-- Skip positions inside the building
			if x < gridPosition.X or x >= gridPosition.X + gridSizeX or
			   z < gridPosition.Y or z >= gridPosition.Y + gridSizeZ then
				local checkPos = Vector2.new(x, z)
				if roads[tostring(checkPos)] then
					return true
				end
			end
		end
	end
	
	return false
end

-- Check if position is within city bounds
function BuildingSystem.IsWithinCityBounds(gridPosition)
	-- Validate input
	if not gridPosition or not gridPosition.X or not gridPosition.Y then
		return false -- Invalid position is outside bounds
	end

	-- This would check against the current city expansion level
	-- For now, using a basic bounds check
	local bounds = Config.ZONE_EXPANSION.STARTING_SIZE
	return gridPosition.X >= -bounds[1]/2 and gridPosition.X < bounds[1]/2 and
		   gridPosition.Y >= -bounds[2]/2 and gridPosition.Y < bounds[2]/2
end

-- Check if position is within player's assigned plot
function BuildingSystem.IsWithinPlayerPlot(worldPosition, playerPlotInfo)
	-- Validate inputs
	if not worldPosition or not playerPlotInfo then
		return false, "Invalid position or plot info"
	end

	if not playerPlotInfo.Position or not playerPlotInfo.PlotNumber then
		return false, "Invalid plot information"
	end

	-- Plot configuration (should match PlotManager)
	local PLOT_SIZE = Vector3.new(200, 1, 200)
	local plotCenter = playerPlotInfo.Position

	-- Calculate plot boundaries
	local plotMinX = plotCenter.X - PLOT_SIZE.X/2
	local plotMaxX = plotCenter.X + PLOT_SIZE.X/2
	local plotMinZ = plotCenter.Z - PLOT_SIZE.Z/2
	local plotMaxZ = plotCenter.Z + PLOT_SIZE.Z/2

	-- Check if position is within plot boundaries
	local isWithin = worldPosition.X >= plotMinX and worldPosition.X <= plotMaxX and
					 worldPosition.Z >= plotMinZ and worldPosition.Z <= plotMaxZ

	if isWithin then
		return true, "Within plot boundaries"
	else
		return false, "Outside your plot boundaries"
	end
end

-- Get plot number from world position
function BuildingSystem.GetPlotFromPosition(worldPosition)
	-- Plot configuration (should match PlotManager)
	local PLOT_SIZE = Vector3.new(200, 1, 200)
	local PLOT_SPACING = 50
	local PLOTS_PER_ROW = 3
	local BASE_POSITION = Vector3.new(0, 0, 0)

	-- Calculate which plot this position belongs to
	local relativeX = worldPosition.X - BASE_POSITION.X
	local relativeZ = worldPosition.Z - BASE_POSITION.Z

	-- Calculate plot grid coordinates
	local plotCol = math.floor(relativeX / (PLOT_SIZE.X + PLOT_SPACING))
	local plotRow = math.floor(relativeZ / (PLOT_SIZE.Z + PLOT_SPACING))

	-- Calculate plot number (1-based)
	local plotNumber = plotRow * PLOTS_PER_ROW + plotCol + 1

	-- Validate plot number
	if plotNumber >= 1 and plotNumber <= 7 then
		return plotNumber
	else
		return nil -- Position is not within any plot
	end
end

-- Calculate building upgrade cost
function BuildingSystem.GetUpgradeCost(buildingType, currentLevel)
	local buildingConfig = Config.BUILDINGS[buildingType]
	if not buildingConfig then
		return nil
	end
	
	local baseCost = buildingConfig.Cost.Pieces or 0
	local upgradeCost = math.floor(baseCost * (1.5 ^ currentLevel))
	
	return {
		Pieces = upgradeCost,
		Metal = currentLevel,
		Plastic = math.floor(currentLevel / 2)
	}
end

-- Calculate service coverage
function BuildingSystem.IsInServiceRange(buildingPosition, servicePosition, serviceType)
	local distance = (buildingPosition - servicePosition).Magnitude
	local coverageRadius = Config.SERVICE_COVERAGE[serviceType] or 50
	return distance <= coverageRadius
end

-- Calculate population bonus from comfort services
function BuildingSystem.GetPopulationBonus(buildingPosition, comfortBuildings)
	local totalBonus = 0
	
	for _, comfortBuilding in pairs(comfortBuildings) do
		local distance = (buildingPosition - comfortBuilding.Position).Magnitude
		local bonusRadius = comfortBuilding.BonusRadius or 30
		
		if distance <= bonusRadius then
			totalBonus += comfortBuilding.PopulationBonus or 2
		end
	end
	
	return totalBonus
end

-- Validate building requirements (level, resources, etc.)
function BuildingSystem.CanBuild(buildingType, playerData)
	local buildingConfig = Config.BUILDINGS[buildingType]
	if not buildingConfig then
		return false, "Invalid building type"
	end
	
	-- Check level requirement
	local playerLevel = BuildingSystem.GetPlayerLevel(playerData.XP)
	if playerLevel < buildingConfig.UnlockLevel then
		return false, "Level requirement not met"
	end
	
	-- Check cost
	for currency, amount in pairs(buildingConfig.Cost) do
		if (playerData[currency] or 0) < amount then
			return false, "Insufficient " .. currency
		end
	end
	
	return true, "Can build"
end

-- Get player level from XP
function BuildingSystem.GetPlayerLevel(xp)
	for level = #Config.LEVEL_REQUIREMENTS, 1, -1 do
		if xp >= Config.LEVEL_REQUIREMENTS[level] then
			return level
		end
	end
	return 1
end

-- Calculate resource production/consumption balance
function BuildingSystem.CalculateResourceBalance(buildings)
	local production = {Energy = 0, Water = 0}
	local consumption = {Energy = 0, Water = 0}
	
	for _, building in pairs(buildings) do
		local config = Config.BUILDINGS[building.Type]
		if config then
			-- Add production
			if config.EnergyProduction then
				production.Energy += config.EnergyProduction * (building.Level or 1)
			end
			if config.WaterProduction then
				production.Water += config.WaterProduction * (building.Level or 1)
			end
			
			-- Add consumption
			if config.EnergyConsumption then
				consumption.Energy += config.EnergyConsumption * (building.Level or 1)
			end
			if config.WaterConsumption then
				consumption.Water += config.WaterConsumption * (building.Level or 1)
			end
		end
	end
	
	return production, consumption
end

return BuildingSystem
