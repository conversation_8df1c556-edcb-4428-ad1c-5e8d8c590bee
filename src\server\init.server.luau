--[[
	UrbanSim Server Initialization
	Main server script that initializes all game systems
]]

print("🏙️ UrbanSim Server Starting...")

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Wait for shared modules to load
local Shared = ReplicatedStorage:WaitForChild("Shared")
local Config = require(Shared:WaitForChild("Config"))

print("📋 Loading configuration...")
print("🎮 Game Version:", Config.GAME_VERSION)

-- Initialize core managers
print("💾 Initializing Data Manager...")
local DataManager = require(script:WaitForChild("DataManager"))

print("🏗️ Initializing Building Manager...")
local BuildingManager = require(script:WaitForChild("BuildingManager"))

print("⚡ Initializing Resource Manager...")
local ResourceManager = require(script:WaitForChild("ResourceManager"))

print("🔬 Initializing Mission Manager...")
local MissionManager = require(script:WaitForChild("MissionManager"))

print("🎁 Initializing Daily Rewards Manager...")
local DailyRewardsManager = require(script:WaitForChild("DailyRewardsManager"))

print("🏆 Initializing Achievement Manager...")
local AchievementManager = require(script:WaitForChild("AchievementManager"))

print("🌐 Initializing Multiplayer Manager...")
local MultiplayerManager = require(script:WaitForChild("MultiplayerManager"))

print("🎫 Initializing Gamepass Manager...")
local GamepassManager = require(script:WaitForChild("GamepassManager"))

print("🏘️ Initializing Plot Manager...")
local PlotManager = require(script:WaitForChild("PlotManager"))

print("🔊 Initializing Sound Events...")
local SoundEvents = require(script:WaitForChild("SoundEvents"))

-- Create Assets folder in ReplicatedStorage if it doesn't exist
local Assets = ReplicatedStorage:FindFirstChild("Assets")
if not Assets then
	Assets = Instance.new("Folder")
	Assets.Name = "Assets"
	Assets.Parent = ReplicatedStorage
end

-- Initialize RemoteEvents and RemoteFunctions
print("📡 Setting up Remote Events...")
local RemoteEvents = require(Shared.Assets:WaitForChild("RemoteEvents"))
local RemoteFunctions = require(Shared.Assets:WaitForChild("RemoteFunctions"))

-- Initialize all systems
print("🚀 Initializing all systems...")
PlotManager.Initialize()
SoundEvents.Initialize()

-- Setup plot RemoteEvent handlers
RemoteEvents.TeleportToPlot.OnServerEvent:Connect(function(player, plotNumber)
	PlotManager.TeleportToPlot(player, plotNumber)
end)

RemoteEvents.ClaimPlot.OnServerEvent:Connect(function(player, plotNumber)
	local success, message = PlotManager.ClaimPlot(player, plotNumber)
	if success then
		RemoteEvents.ShowNotification:FireClient(player, "Success", message)
	else
		RemoteEvents.ShowNotification:FireClient(player, "Error", message)
	end
end)

RemoteEvents.ReleasePlot.OnServerEvent:Connect(function(player)
	local success, message = PlotManager.ReleasePlot(player)
	if success then
		RemoteEvents.ShowNotification:FireClient(player, "Success", message)
	else
		RemoteEvents.ShowNotification:FireClient(player, "Error", message)
	end
end)

RemoteFunctions.GetPlayerPlotInfo.OnServerInvoke = function(player)
	return PlotManager.GetPlayerPlotInfo(player)
end

RemoteFunctions.GetPlotInfo.OnServerInvoke = function(player, plotNumber)
	return PlotManager.GetPlotInfo(plotNumber)
end

RemoteFunctions.GetAllPlotsInfo.OnServerInvoke = function(player)
	return PlotManager.GetAllPlotsInfo()
end

print("✅ UrbanSim Server Initialized Successfully!")
print("🌟 Ready for players to join!")