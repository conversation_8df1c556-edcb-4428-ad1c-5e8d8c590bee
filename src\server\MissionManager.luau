--[[
	Mission Manager
	Handles the "Mad Scientist" disaster system and missions
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local Config = require(ReplicatedStorage:WaitFor<PERSON>hild("Shared"):WaitF<PERSON><PERSON>hild("Config"))
local DataManager = require(script.Parent:Wait<PERSON><PERSON><PERSON>hil<PERSON>("DataManager"))

-- Get RemoteEvents
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))

local MissionManager = {}

-- Active missions per player
local ActiveMissions = {}
local MissionTimers = {}

-- Disaster types and their effects
local DISASTERS = {
	Tornado = {
		Name = "Tornado",
		Description = "A tornado has damaged several buildings!",
		DamageRadius = 30,
		DamageAmount = 50,
		RepairCost = Config.MISSIONS.REPAIR_COSTS.Tornado
	},
	
	Fire = {
		Name = "Fire",
		Description = "A fire has spread through the city!",
		DamageRadius = 25,
		DamageAmount = 40,
		RepairCost = Config.MISSIONS.REPAIR_COSTS.Fire
	},
	
	Earthquake = {
		Name = "Earthquake",
		Description = "An earthquake has shaken the city!",
		DamageRadius = 50,
		DamageAmount = 30,
		RepairCost = Config.MISSIONS.REPAIR_COSTS.Earthquake
	},
	
	Robot_Invasion = {
		Name = "Robot Invasion",
		Description = "Robots are attacking the city!",
		DamageRadius = 20,
		DamageAmount = 60,
		RepairCost = Config.MISSIONS.REPAIR_COSTS.Robot_Invasion
	}
}

-- Start mission system for a player
function MissionManager.StartMissionSystem(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	-- Schedule first disaster
	MissionManager.ScheduleNextDisaster(player)
end

-- Schedule the next disaster for a player
function MissionManager.ScheduleNextDisaster(player)
	local minInterval = Config.MISSIONS.DISASTER_INTERVAL[1]
	local maxInterval = Config.MISSIONS.DISASTER_INTERVAL[2]
	local interval = math.random(minInterval, maxInterval)
	
	local timerKey = tostring(player.UserId)
	
	-- Clear existing timer
	if MissionTimers[timerKey] then
		task.cancel(MissionTimers[timerKey])
	end
	
	-- Schedule new disaster
	MissionTimers[timerKey] = task.delay(interval, function()
		MissionManager.TriggerDisaster(player)
	end)
end

-- Trigger a disaster for a player
function MissionManager.TriggerDisaster(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	-- Don't trigger if player already has an active mission
	if ActiveMissions[player.UserId] then return end
	
	-- Select random disaster type
	local disasterTypes = Config.MISSIONS.DISASTER_TYPES
	local selectedType = disasterTypes[math.random(1, #disasterTypes)]
	local disaster = DISASTERS[selectedType]
	
	if not disaster then return end
	
	-- Find buildings to damage
	local damagedBuildings = MissionManager.SelectBuildingsForDamage(player, disaster)
	
	if #damagedBuildings == 0 then
		-- No buildings to damage, schedule next disaster
		MissionManager.ScheduleNextDisaster(player)
		return
	end
	
	-- Create mission
	local mission = {
		Id = tostring(tick()) .. "_" .. selectedType,
		Type = selectedType,
		Disaster = disaster,
		DamagedBuildings = damagedBuildings,
		StartTime = tick(),
		Player = player.UserId,
		Completed = false
	}
	
	-- Apply damage to buildings
	for _, buildingId in ipairs(damagedBuildings) do
		local building = playerData.Buildings[buildingId]
		if building then
			building.Health = math.max(0, building.Health - disaster.DamageAmount)
			building.Active = building.Health > 0
		end
	end
	
	-- Store active mission
	ActiveMissions[player.UserId] = mission
	playerData.ActiveMission = mission
	
	-- Notify client
	RemoteEvents.DisasterOccurred:FireClient(player, mission)
	RemoteEvents.ShowNotification:FireClient(player, "Disaster", disaster.Description)
	
	print("Disaster triggered for", player.Name, ":", selectedType)
end

-- Select buildings to damage based on disaster
function MissionManager.SelectBuildingsForDamage(player, disaster)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return {} end
	
	local buildings = {}
	for buildingId, building in pairs(playerData.Buildings) do
		if building.Active and building.Health > 0 then
			table.insert(buildings, {Id = buildingId, Building = building})
		end
	end
	
	if #buildings == 0 then return {} end
	
	-- Select a random epicenter
	local epicenterBuilding = buildings[math.random(1, #buildings)]
	local epicenterPos = epicenterBuilding.Building.Position
	
	-- Find buildings within damage radius
	local damagedBuildings = {}
	for _, buildingData in ipairs(buildings) do
		local distance = (buildingData.Building.Position - epicenterPos).Magnitude
		if distance <= disaster.DamageRadius then
			table.insert(damagedBuildings, buildingData.Id)
		end
	end
	
	return damagedBuildings
end

-- Repair a damaged building
function MissionManager.RepairBuilding(player, buildingId)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end
	
	local mission = ActiveMissions[player.UserId]
	if not mission then
		return false, "No active mission"
	end
	
	-- Check if building is part of the mission
	local isMissionBuilding = false
	for _, missionBuildingId in ipairs(mission.DamagedBuildings) do
		if missionBuildingId == buildingId then
			isMissionBuilding = true
			break
		end
	end
	
	if not isMissionBuilding then
		return false, "Building not part of current mission"
	end
	
	local building = playerData.Buildings[buildingId]
	if not building then
		return false, "Building not found"
	end
	
	if building.Health >= 100 then
		return false, "Building already repaired"
	end
	
	-- Check if player has repair materials
	local repairCost = mission.Disaster.RepairCost
	if not DataManager.CanAfford(player, repairCost) then
		return false, "Insufficient repair materials"
	end
	
	-- Spend repair materials
	if not DataManager.SpendResources(player, repairCost) then
		return false, "Failed to spend materials"
	end
	
	-- Repair building
	building.Health = 100
	building.Active = true
	
	-- Check if all buildings are repaired
	local allRepaired = true
	for _, missionBuildingId in ipairs(mission.DamagedBuildings) do
		local missionBuilding = playerData.Buildings[missionBuildingId]
		if missionBuilding and missionBuilding.Health < 100 then
			allRepaired = false
			break
		end
	end
	
	if allRepaired then
		MissionManager.CompleteMission(player)
	end
	
	return true, "Building repaired"
end

-- Complete a mission
function MissionManager.CompleteMission(player)
	local mission = ActiveMissions[player.UserId]
	if not mission then return end
	
	-- Award rewards
	local rewards = {
		Cles = 1,
		XP = Config.XP_REWARDS.COMPLETE_MISSION,
		Metal = math.random(1, 3),
		Plastic = math.random(1, 2)
	}
	
	for currency, amount in pairs(rewards) do
		DataManager.AddToPlayer(player, currency, amount)
	end
	
	-- Clear mission
	ActiveMissions[player.UserId] = nil
	local playerData = DataManager.GetPlayerData(player)
	if playerData then
		playerData.ActiveMission = nil
		table.insert(playerData.CompletedMissions, mission.Id)
	end
	
	-- Notify client
	RemoteEvents.ShowNotification:FireClient(player, "Success", "Mission completed! Rewards received.")
	
	-- Schedule next disaster
	MissionManager.ScheduleNextDisaster(player)
	
	print("Mission completed for", player.Name)
end

-- Clean up player missions when they leave
function MissionManager.CleanupPlayer(player)
	local userId = player.UserId
	
	-- Clear active mission
	ActiveMissions[userId] = nil
	
	-- Cancel mission timer
	local timerKey = tostring(userId)
	if MissionTimers[timerKey] then
		task.cancel(MissionTimers[timerKey])
		MissionTimers[timerKey] = nil
	end
end

-- Remote event handlers
RemoteEvents.RepairBuilding.OnServerEvent:Connect(function(player, buildingId)
	local success, message = MissionManager.RepairBuilding(player, buildingId)
	if success then
		RemoteEvents.ShowNotification:FireClient(player, "Success", message)
	else
		RemoteEvents.ShowNotification:FireClient(player, "Error", message)
	end
end)

-- Player events
game.Players.PlayerAdded:Connect(function(player)
	-- Wait for data to load, then start mission system
	task.wait(5)
	MissionManager.StartMissionSystem(player)
end)

game.Players.PlayerRemoving:Connect(function(player)
	MissionManager.CleanupPlayer(player)
end)

return MissionManager
