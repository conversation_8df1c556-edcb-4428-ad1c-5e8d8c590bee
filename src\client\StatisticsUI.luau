-- StatisticsUI.luau
-- Client-side statistics frame system

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("PlayerGui")

local StatisticsUI = {}

-- Statistics state
local statisticsFrame = nil
local statisticsButton = nil
local isOpen = false

-- Create statistics toggle button
function StatisticsUI.CreateStatisticsButton()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then
		warn("📊 UrbanSimUI not found for statistics button!")
		return
	end

	-- Create statistics button (replaces TopBar)
	statisticsButton = Instance.new("TextButton")
	statisticsButton.Name = "StatisticsButton"
	statisticsButton.Size = UDim2.new(0, 150, 0, 40)
	statisticsButton.Position = UDim2.new(0, 10, 0, 10)
	statisticsButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
	statisticsButton.Text = "📊 Statistics"
	statisticsButton.TextColor3 = Color3.new(1, 1, 1)
	statisticsButton.TextScaled = true
	statisticsButton.Font = Enum.Font.SourceSansBold
	statisticsButton.ZIndex = 5
	statisticsButton.Parent = screenGui

	-- Add corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 6)
	corner.Parent = statisticsButton

	-- Button functionality
	statisticsButton.MouseButton1Click:Connect(function()
		StatisticsUI.ToggleStatistics()
	end)

	-- Hover effects
	statisticsButton.MouseEnter:Connect(function()
		TweenService:Create(statisticsButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.3, 0.7, 0.9)
		}):Play()
	end)

	statisticsButton.MouseLeave:Connect(function()
		TweenService:Create(statisticsButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
		}):Play()
	end)

	print("📊 Statistics button created!")
end

-- Create statistics frame
function StatisticsUI.CreateStatisticsFrame()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end

	-- Create statistics frame
	statisticsFrame = Instance.new("Frame")
	statisticsFrame.Name = "StatisticsFrame"
	statisticsFrame.Size = UDim2.new(1, 0, 0, 80)
	statisticsFrame.Position = UDim2.new(0, 0, 0, 0)
	statisticsFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	statisticsFrame.BorderSizePixel = 0
	statisticsFrame.Visible = false
	statisticsFrame.ZIndex = 3
	statisticsFrame.Parent = screenGui

	-- Add gradient
	local gradient = Instance.new("UIGradient")
	gradient.Color = ColorSequence.new{
		ColorSequenceKeypoint.new(0, Color3.new(0.15, 0.15, 0.2)),
		ColorSequenceKeypoint.new(1, Color3.new(0.1, 0.1, 0.15))
	}
	gradient.Rotation = 90
	gradient.Parent = statisticsFrame

	-- Currency displays (same as original TopBar)
	local currencies = {"Pieces", "Cash", "XP", "Population", "Level", "Energy", "Water"}
	local currencyIcons = {"💰", "💎", "⭐", "👥", "🏆", "⚡", "💧"}

	-- Mobile-responsive currency frame sizing
	local isMobile = UserInputService.TouchEnabled and not UserInputService.KeyboardEnabled
	local frameWidth = isMobile and 85 or 100
	local frameSpacing = isMobile and 95 or 110
	local frameMargin = isMobile and 3 or 5

	for i, currency in ipairs(currencies) do
		local currencyFrame = Instance.new("Frame")
		currencyFrame.Name = currency .. "Frame"
		currencyFrame.Size = UDim2.new(0, frameWidth, 1, -frameMargin)
		currencyFrame.Position = UDim2.new(0, (i-1) * frameSpacing + frameMargin, 0, frameMargin/2)
		currencyFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
		currencyFrame.BorderSizePixel = 0
		currencyFrame.Parent = statisticsFrame

		-- Add corner radius
		local currencyCorner = Instance.new("UICorner")
		currencyCorner.CornerRadius = UDim.new(0, 8)
		currencyCorner.Parent = currencyFrame

		-- Add gradient
		local currencyGradient = Instance.new("UIGradient")
		currencyGradient.Color = ColorSequence.new{
			ColorSequenceKeypoint.new(0, Color3.new(0.2, 0.2, 0.25)),
			ColorSequenceKeypoint.new(1, Color3.new(0.1, 0.1, 0.15))
		}
		currencyGradient.Rotation = 90
		currencyGradient.Parent = currencyFrame

		-- Currency icon
		local currencyIcon = Instance.new("TextLabel")
		currencyIcon.Name = currency .. "Icon"
		currencyIcon.Size = UDim2.new(0, 25, 1, 0)
		currencyIcon.Position = UDim2.new(0, 5, 0, 0)
		currencyIcon.BackgroundTransparency = 1
		currencyIcon.Text = currencyIcons[i]
		currencyIcon.TextColor3 = Color3.new(1, 1, 1)
		currencyIcon.TextScaled = true
		currencyIcon.Font = Enum.Font.SourceSansBold
		currencyIcon.Parent = currencyFrame

		-- Currency label
		local currencyLabel = Instance.new("TextLabel")
		currencyLabel.Name = currency .. "Label"
		currencyLabel.Size = UDim2.new(1, -30, 1, 0)
		currencyLabel.Position = UDim2.new(0, 30, 0, 0)
		currencyLabel.BackgroundTransparency = 1
		currencyLabel.Text = "0"
		currencyLabel.TextColor3 = Color3.new(1, 1, 1)
		currencyLabel.TextScaled = true
		currencyLabel.Font = Enum.Font.SourceSansBold
		currencyLabel.TextXAlignment = Enum.TextXAlignment.Left
		currencyLabel.Parent = currencyFrame

		-- Add hover effect
		currencyFrame.MouseEnter:Connect(function()
			TweenService:Create(currencyFrame, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
			}):Play()
		end)

		currencyFrame.MouseLeave:Connect(function()
			TweenService:Create(currencyFrame, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
			}):Play()
		end)
	end

	-- Action buttons section
	local buttonsFrame = Instance.new("Frame")
	buttonsFrame.Name = "ButtonsFrame"
	buttonsFrame.Size = UDim2.new(0, 400, 1, -10)
	buttonsFrame.Position = UDim2.new(1, -410, 0, 5)
	buttonsFrame.BackgroundTransparency = 1
	buttonsFrame.Parent = statisticsFrame

	-- Crafting Menu Button
	local craftingMenuButton = Instance.new("TextButton")
	craftingMenuButton.Name = "CraftingMenuButton"
	craftingMenuButton.Size = UDim2.new(0, 100, 0, 40)
	craftingMenuButton.Position = UDim2.new(0, 0, 0, 5)
	craftingMenuButton.BackgroundColor3 = Color3.new(0.6, 0.4, 0.2)
	craftingMenuButton.Text = "🏭 Craft"
	craftingMenuButton.TextColor3 = Color3.new(1, 1, 1)
	craftingMenuButton.TextScaled = true
	craftingMenuButton.Font = Enum.Font.SourceSansBold
	craftingMenuButton.Parent = buttonsFrame

	local craftingCorner = Instance.new("UICorner")
	craftingCorner.CornerRadius = UDim.new(0, 6)
	craftingCorner.Parent = craftingMenuButton

	-- Building Menu Button
	local buildingMenuButton = Instance.new("TextButton")
	buildingMenuButton.Name = "BuildingMenuButton"
	buildingMenuButton.Size = UDim2.new(0, 100, 0, 40)
	buildingMenuButton.Position = UDim2.new(0, 110, 0, 5)
	buildingMenuButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.4)
	buildingMenuButton.Text = "🏗️ Build"
	buildingMenuButton.TextColor3 = Color3.new(1, 1, 1)
	buildingMenuButton.TextScaled = true
	buildingMenuButton.Font = Enum.Font.SourceSansBold
	buildingMenuButton.Parent = buttonsFrame

	local buildingCorner = Instance.new("UICorner")
	buildingCorner.CornerRadius = UDim.new(0, 6)
	buildingCorner.Parent = buildingMenuButton

	-- Plot Menu Button
	local plotMenuButton = Instance.new("TextButton")
	plotMenuButton.Name = "PlotMenuButton"
	plotMenuButton.Size = UDim2.new(0, 100, 0, 40)
	plotMenuButton.Position = UDim2.new(0, 220, 0, 5)
	plotMenuButton.BackgroundColor3 = Color3.new(0.6, 0.2, 0.6)
	plotMenuButton.Text = "🏘️ Plots"
	plotMenuButton.TextColor3 = Color3.new(1, 1, 1)
	plotMenuButton.TextScaled = true
	plotMenuButton.Font = Enum.Font.SourceSansBold
	plotMenuButton.Parent = buttonsFrame

	local plotCorner = Instance.new("UICorner")
	plotCorner.CornerRadius = UDim.new(0, 6)
	plotCorner.Parent = plotMenuButton

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -35, 0, 5)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.3, 0.3)
	closeButton.Text = "✕"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = statisticsFrame

	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton

	-- Button functionality
	craftingMenuButton.MouseButton1Click:Connect(function()
		-- Trigger crafting UI
		local CraftingUI = require(script.Parent:WaitForChild("CraftingUI"))
		CraftingUI.ShowCraftingWindow()
	end)

	buildingMenuButton.MouseButton1Click:Connect(function()
		-- Trigger building UI
		local BuildingUI = require(script.Parent:WaitForChild("BuildingUI"))
		BuildingUI.OpenBuildingWindow()
	end)

	plotMenuButton.MouseButton1Click:Connect(function()
		-- Trigger plot UI
		local PlotUI = require(script.Parent:WaitForChild("PlotUI"))
		PlotUI.OpenPlotWindow()
	end)

	closeButton.MouseButton1Click:Connect(function()
		StatisticsUI.CloseStatistics()
	end)

	print("📊 Statistics frame created!")
end

-- Toggle statistics frame
function StatisticsUI.ToggleStatistics()
	if isOpen then
		StatisticsUI.CloseStatistics()
	else
		StatisticsUI.OpenStatistics()
	end
end

-- Open statistics frame
function StatisticsUI.OpenStatistics()
	if not statisticsFrame then
		StatisticsUI.CreateStatisticsFrame()
	end

	isOpen = true
	statisticsFrame.Visible = true
	statisticsButton.Text = "📊 Hide Stats"

	-- Update statistics data
	StatisticsUI.UpdateStatistics()

	-- Animate in
	statisticsFrame.Position = UDim2.new(0, 0, 0, -80)
	TweenService:Create(statisticsFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
		Position = UDim2.new(0, 0, 0, 0)
	}):Play()

	print("📊 Statistics opened")
end

-- Close statistics frame
function StatisticsUI.CloseStatistics()
	if not statisticsFrame then return end

	isOpen = false
	statisticsButton.Text = "📊 Statistics"

	-- Animate out
	TweenService:Create(statisticsFrame, TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.In), {
		Position = UDim2.new(0, 0, 0, -80)
	}).Completed:Connect(function()
		statisticsFrame.Visible = false
	end)

	print("📊 Statistics closed")
end

-- Update statistics data
function StatisticsUI.UpdateStatistics()
	if not statisticsFrame or not statisticsFrame.Visible then return end

	-- Update currency displays using leaderstats
	local leaderstats = player:FindFirstChild("leaderstats")
	if leaderstats then
		local currencies = {"Pieces", "Cash", "XP", "Population", "Level", "Energy", "Water"}
		for _, currency in ipairs(currencies) do
			local currencyFrame = statisticsFrame:FindFirstChild(currency .. "Frame")
			if currencyFrame then
				local label = currencyFrame:FindFirstChild(currency .. "Label")
				local leaderstatValue = leaderstats:FindFirstChild(currency)

				if label and leaderstatValue then
					-- Format large numbers
					local value = leaderstatValue.Value
					local formattedValue = value

					if value >= 1000000 then
						formattedValue = string.format("%.1fM", value / 1000000)
					elseif value >= 1000 then
						formattedValue = string.format("%.1fK", value / 1000)
					end

					label.Text = formattedValue
				end
			end
		end
	end
end

-- Initialize statistics system
function StatisticsUI.Initialize()
	print("📊 Initializing Statistics UI...")

	-- Create statistics button
	StatisticsUI.CreateStatisticsButton()

	-- Handle keyboard shortcut
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end

		-- Toggle statistics with Tab key
		if input.KeyCode == Enum.KeyCode.Tab then
			StatisticsUI.ToggleStatistics()
		end
	end)

	-- Monitor leaderstats changes
	local leaderstats = player:WaitForChild("leaderstats", 10)
	if leaderstats then
		local currencies = {"Pieces", "Cash", "XP", "Population", "Level", "Energy", "Water"}
		for _, currency in ipairs(currencies) do
			local leaderstatValue = leaderstats:FindFirstChild(currency)
			if leaderstatValue then
				leaderstatValue.Changed:Connect(function()
					StatisticsUI.UpdateStatistics()
				end)
			end
		end
	end

	print("✅ Statistics UI initialized!")
end

return StatisticsUI
