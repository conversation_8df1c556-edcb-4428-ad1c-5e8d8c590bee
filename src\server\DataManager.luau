--[[
	Data Manager
	Handles all player data persistence using ProfileService
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local Config = require(ReplicatedStorage:WaitFor<PERSON>hild("Shared"):WaitFor<PERSON>hild("Config"))
local BuildingSystem = require(ReplicatedStorage:WaitForChild("Shared"):WaitFor<PERSON>hild("BuildingSystem"))

-- ProfileService would be required here in a real implementation
-- For now, we'll use a simple DataStore wrapper
local DataStoreService = game:GetService("DataStoreService")
local PlayerDataStore = DataStoreService:GetDataStore("UrbanSimPlayerData_v1")

local DataManager = {}
DataManager.PlayerProfiles = {}
DataManager.LoadedPlayers = {}

-- Helper functions for serializing/deserializing Vector data
function DataManager.SerializeVector3(vector3)
	if not vector3 then return nil end
	return {X = vector3.X, Y = vector3.Y, Z = vector3.Z}
end

function DataManager.DeserializeVector3(data)
	if not data or not data.X then return Vector3.new(0, 0, 0) end
	return Vector3.new(data.X, data.Y, data.Z)
end

function DataManager.SerializeVector2(vector2)
	if not vector2 then return nil end
	return {X = vector2.X, Y = vector2.Y}
end

function DataManager.DeserializeVector2(data)
	if not data or not data.X then return Vector2.new(0, 0) end
	return Vector2.new(data.X, data.Y)
end

-- Clean data for DataStore (remove userdata)
function DataManager.CleanDataForSave(data)
	local cleanData = {}

	for key, value in pairs(data) do
		if key == "Buildings" then
			-- Buildings table needs special handling
			cleanData[key] = {}
			for buildingId, buildingData in pairs(value) do
				cleanData[key][buildingId] = {}
				for buildingKey, buildingValue in pairs(buildingData) do
					-- Convert Vector3/Vector2 to serializable tables
					if buildingKey == "Position" and typeof(buildingValue) == "Vector3" then
						cleanData[key][buildingId][buildingKey] = DataManager.SerializeVector3(buildingValue)
					elseif buildingKey == "GridPosition" and typeof(buildingValue) == "Vector2" then
						cleanData[key][buildingId][buildingKey] = DataManager.SerializeVector2(buildingValue)
					else
						cleanData[key][buildingId][buildingKey] = buildingValue
					end
				end
			end
		else
			cleanData[key] = value
		end
	end

	return cleanData
end

-- DataStore throttling
local SAVE_INTERVAL = 30 -- Save every 30 seconds
local LAST_SAVE_TIME = {}
local PENDING_SAVES = {}
local SAVE_QUEUE = {}

-- Default player data template
local DEFAULT_DATA = {
	-- Currencies
	Pieces = Config.STARTING_VALUES.Pieces,
	Cash = Config.STARTING_VALUES.Cash,
	Cles = Config.STARTING_VALUES.Cles,
	ClesDiamant = Config.STARTING_VALUES.ClesDiamant,
	GammaCoin = Config.STARTING_VALUES.GammaCoin,
	
	-- Stats
	XP = Config.STARTING_VALUES.XP,
	Population = Config.STARTING_VALUES.Population,
	ProductionEnergie = Config.STARTING_VALUES.ProductionEnergie,
	ProductionEau = Config.STARTING_VALUES.ProductionEau,
	
	-- Game Data
	Buildings = {},
	Roads = {},
	Resources = {
		Metal = 0,
		Plastic = 0,
		Wood = 0,
		CarteMere = 0,
		Metre = 0,
		PC = 0
	},
	
	-- Progress
	UnlockedBuildings = {"HOUSE_SMALL", "CITY_HALL", "POWER_PLANT", "WATER_PLANT"},
	CityBounds = Config.ZONE_EXPANSION.STARTING_SIZE,
	LastTaxCollection = 0,
	
	-- Crafting
	CraftingQueue = {},
	
	-- Missions
	CompletedMissions = {},
	ActiveMission = nil,
	
	-- Clan
	ClanId = nil,
	ClanRole = nil,
	
	-- Settings
	Settings = {
		AutoCollect = false,
		Notifications = true
	}
}

-- Load player data
function DataManager.LoadPlayerData(player)
	local success, data = pcall(function()
		return PlayerDataStore:GetAsync(tostring(player.UserId))
	end)
	
	if success and data then
		-- Merge with default data to ensure all fields exist
		for key, value in pairs(DEFAULT_DATA) do
			if data[key] == nil then
				data[key] = value
			end
		end

		-- Deserialize Vector data in Buildings
		if data.Buildings then
			for buildingId, buildingData in pairs(data.Buildings) do
				if buildingData.Position and type(buildingData.Position) == "table" then
					buildingData.Position = DataManager.DeserializeVector3(buildingData.Position)
				end
				if buildingData.GridPosition and type(buildingData.GridPosition) == "table" then
					buildingData.GridPosition = DataManager.DeserializeVector2(buildingData.GridPosition)
				end
			end
		end

		DataManager.PlayerProfiles[player] = data
	else
		-- Create new profile with default data
		DataManager.PlayerProfiles[player] = {}
		for key, value in pairs(DEFAULT_DATA) do
			DataManager.PlayerProfiles[player][key] = value
		end
		warn("Failed to load data for " .. player.Name .. ", using default data")
	end
	
	DataManager.LoadedPlayers[player] = true
	DataManager.SetupLeaderstats(player)
	
	return DataManager.PlayerProfiles[player]
end

-- Save player data with throttling
function DataManager.SavePlayerData(player, forceImmediate)
	if not DataManager.PlayerProfiles[player] then
		return false
	end

	local userId = tostring(player.UserId)
	local currentTime = tick()

	-- Check if we should throttle this save
	if not forceImmediate then
		local lastSave = LAST_SAVE_TIME[userId] or 0
		if currentTime - lastSave < SAVE_INTERVAL then
			-- Queue for later save
			PENDING_SAVES[userId] = player
			return true
		end
	end

	-- Clean data for DataStore and perform the save
	local success, error = pcall(function()
		local cleanData = DataManager.CleanDataForSave(DataManager.PlayerProfiles[player])
		PlayerDataStore:SetAsync(userId, cleanData)
	end)

	if success then
		LAST_SAVE_TIME[userId] = currentTime
		PENDING_SAVES[userId] = nil
		print("✅ Saved data for " .. player.Name)
		return true
	else
		warn("Failed to save data for " .. player.Name .. ": " .. tostring(error))
		-- Queue for retry
		PENDING_SAVES[userId] = player
		return false
	end
end

-- Process pending saves
function DataManager.ProcessPendingSaves()
	local currentTime = tick()

	for userId, player in pairs(PENDING_SAVES) do
		local lastSave = LAST_SAVE_TIME[userId] or 0
		if currentTime - lastSave >= SAVE_INTERVAL then
			DataManager.SavePlayerData(player, true)
		end
	end
end

-- Get player data
function DataManager.GetPlayerData(player)
	return DataManager.PlayerProfiles[player]
end

-- Update player data
function DataManager.UpdatePlayerData(player, key, value)
	if DataManager.PlayerProfiles[player] then
		DataManager.PlayerProfiles[player][key] = value
		DataManager.UpdateLeaderstats(player, key, value)
		return true
	end
	return false
end

-- Add to player currency/resource
function DataManager.AddToPlayer(player, key, amount)
	local data = DataManager.PlayerProfiles[player]
	if data and data[key] then
		data[key] = math.max(0, data[key] + amount)
		DataManager.UpdateLeaderstats(player, key, data[key])
		return data[key]
	end
	return nil
end

-- Check if player can afford cost
function DataManager.CanAfford(player, cost)
	local data = DataManager.PlayerProfiles[player]
	if not data then return false end
	
	for currency, amount in pairs(cost) do
		if (data[currency] or 0) < amount then
			return false
		end
	end
	return true
end

-- Spend player resources
function DataManager.SpendResources(player, cost)
	if not DataManager.CanAfford(player, cost) then
		return false
	end
	
	local data = DataManager.PlayerProfiles[player]
	for currency, amount in pairs(cost) do
		data[currency] = data[currency] - amount
		DataManager.UpdateLeaderstats(player, currency, data[currency])
	end
	
	return true
end

-- Setup leaderstats
function DataManager.SetupLeaderstats(player)
	local leaderstats = Instance.new("Folder")
	leaderstats.Name = "leaderstats"
	leaderstats.Parent = player

	local data = DataManager.PlayerProfiles[player]

	-- Create IntValues for each stat (including Energy and Water)
	local stats = {"Pieces", "Cash", "Cles", "ClesDiamant", "GammaCoin", "XP", "Population", "Level", "Energy", "Water"}

	for _, statName in ipairs(stats) do
		local stat = Instance.new("IntValue")
		if statName == "Level" then
			-- Calculate level from XP
			stat.Value = BuildingSystem.GetPlayerLevel(data.XP or 0)
		elseif statName == "Energy" then
			stat.Value = data.NetEnergyProduction or 0
		elseif statName == "Water" then
			stat.Value = data.NetWaterProduction or 0
		else
			stat.Value = data[statName] or 0
		end
		stat.Name = statName
		stat.Parent = leaderstats
	end

	print("📊 Setup leaderstats for", player.Name, "- Level:", BuildingSystem.GetPlayerLevel(data.XP or 0))
end

-- Update leaderstats
function DataManager.UpdateLeaderstats(player, key, value)
	local leaderstats = player:FindFirstChild("leaderstats")
	if leaderstats then
		local stat = leaderstats:FindFirstChild(key)
		if stat then
			stat.Value = value
		end

		-- Update level when XP changes
		if key == "XP" then
			local levelStat = leaderstats:FindFirstChild("Level")
			if levelStat then
				local newLevel = BuildingSystem.GetPlayerLevel(value)
				levelStat.Value = newLevel
				print("📊 Player", player.Name, "is now level", newLevel, "with", value, "XP")
			end
		end
	end
end

-- Auto-save system with throttling
local lastAutoSave = 0
RunService.Heartbeat:Connect(function()
	local now = tick()
	if now - lastAutoSave >= Config.AUTOSAVE_INTERVAL then
		lastAutoSave = now
		-- Process pending saves first
		DataManager.ProcessPendingSaves()

		-- Then trigger saves for all players (will be throttled automatically)
		for player, _ in pairs(DataManager.LoadedPlayers) do
			if player.Parent then -- Player still in game
				DataManager.SavePlayerData(player)
			end
		end
	end
end)

-- Player joining
Players.PlayerAdded:Connect(function(player)
	DataManager.LoadPlayerData(player)
end)

-- Player leaving
Players.PlayerRemoving:Connect(function(player)
	if DataManager.LoadedPlayers[player] then
		DataManager.SavePlayerData(player, true) -- Force immediate save
		DataManager.PlayerProfiles[player] = nil
		DataManager.LoadedPlayers[player] = nil
	end
end)

-- Game shutdown
game:BindToClose(function()
	for player, _ in pairs(DataManager.LoadedPlayers) do
		DataManager.SavePlayerData(player, true) -- Force immediate save
	end
	task.wait(2) -- Give time for saves to complete
end)

-- Setup RemoteFunction handlers
task.spawn(function()
	-- Wait for RemoteFunctions to be created
	local Assets = ReplicatedStorage:WaitForChild("Assets")
	local RemoteFunctions = require(Assets:WaitForChild("RemoteFunctions"))

	-- Get player data
	RemoteFunctions.GetPlayerData.OnServerInvoke = function(player)
		return DataManager.GetPlayerData(player)
	end

	-- Check if player can afford something
	RemoteFunctions.CanAfford.OnServerInvoke = function(player, cost)
		return DataManager.CanAfford(player, cost)
	end
end)

-- Give XP to player and update level
function DataManager.GiveXP(player, amount, reason)
	local data = DataManager.PlayerProfiles[player]
	if not data then return false end

	local oldXP = data.XP or 0
	local oldLevel = BuildingSystem.GetPlayerLevel(oldXP)

	data.XP = oldXP + amount
	DataManager.UpdateLeaderstats(player, "XP", data.XP)

	local newLevel = BuildingSystem.GetPlayerLevel(data.XP)

	-- Check for level up
	if newLevel > oldLevel then
		print("🎉 LEVEL UP!", player.Name, "reached level", newLevel)
		-- Fire level up event to client
		local Assets = ReplicatedStorage:WaitForChild("Assets")
		local RemoteEvents = Assets:WaitForChild("RemoteEvents")
		RemoteEvents.ShowNotification:FireClient(player, "Success", "🎉 Level Up! You are now level " .. newLevel)
	end

	print("📊 Gave", amount, "XP to", player.Name, "for", reason or "unknown reason", "- Total XP:", data.XP, "Level:", newLevel)
	return true
end

-- Get player level
function DataManager.GetPlayerLevel(player)
	local data = DataManager.PlayerProfiles[player]
	if not data then return 1 end

	return BuildingSystem.GetPlayerLevel(data.XP or 0)
end

return DataManager
