--[[
	UrbanSim Client Initialization
	Main client script that initializes the game UI and client systems
]]

print("🏙️ UrbanSim Client Starting...")

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for shared modules
local Shared = ReplicatedStorage:WaitForChild("Shared")
local Config = require(Shared:WaitForChild("Config"))
local BuildingSystem = require(Shared:WaitForChild("BuildingSystem"))
local CraftingSystem = require(Shared:WaitForChild("CraftingSystem"))

-- Load client modules
local CraftingUI = require(script:WaitForChild("CraftingUI"))
local DailyRewardsUI = require(script:WaitF<PERSON><PERSON>hild("DailyRewardsUI"))
local AnimationSystem = require(script:WaitF<PERSON><PERSON>hild("AnimationSystem"))
local SettingsUI = require(script:WaitForChild("SettingsUI"))
local BuildingDebugger = require(script:WaitForChild("BuildingDebugger"))
local BuildingUI = require(script:WaitForChild("BuildingUI"))
local GamepassShopUI = require(script:WaitForChild("GamepassShopUI"))
local MobileBuildingControls = require(script:WaitForChild("MobileBuildingControls"))

-- Initialize RemoteEvents and RemoteFunctions
local Assets = ReplicatedStorage:WaitForChild("Assets")

-- Initialize the modules to create the RemoteEvents/Functions
require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Assets"):WaitForChild("RemoteEvents"))
require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Assets"):WaitForChild("RemoteFunctions"))

-- Access RemoteEvents as instances
local RemoteEvents = {
	ShowNotification = Assets:WaitForChild("ShowNotification"),
	PlaceBuilding = Assets:WaitForChild("PlaceBuilding"),
	SellBuilding = Assets:WaitForChild("SellBuilding"),
	UpgradeBuilding = Assets:WaitForChild("UpgradeBuilding"),
	CollectIncome = Assets:WaitForChild("CollectIncome"),
	ClaimDailyReward = Assets:WaitForChild("ClaimDailyReward"),
	ClaimMinuteReward = Assets:WaitForChild("ClaimMinuteReward"),
	StartBuildingPlacement = Assets:WaitForChild("StartBuildingPlacement"),
	BuildingPlaced = Assets:WaitForChild("BuildingPlaced"),
	CurrencyUpdated = Assets:WaitForChild("CurrencyUpdated")
}

-- Access RemoteFunctions as instances
local RemoteFunctions = {
	GetPlayerData = Assets:WaitForChild("GetPlayerData"),
	GetDailyStatus = Assets:WaitForChild("GetDailyStatus"),
	GetMinuteStatus = Assets:WaitForChild("GetMinuteStatus"),
	GetGamepassShop = Assets:WaitForChild("GetGamepassShop"),
	CanPlaceBuilding = Assets:WaitForChild("CanPlaceBuilding"),
	CanBuild = Assets:WaitForChild("CanBuild"),
	GetBuildingCost = Assets:WaitForChild("GetBuildingCost")
}

print("📱 Initializing Client UI...")

-- Initialize UI modules
print("🎁 Initializing Daily Rewards UI...")
-- DailyRewardsUI auto-initializes

print("🛒 Initializing Gamepass Shop UI...")
-- GamepassShopUI auto-initializes

print("🔧 Initializing Building Debugger...")
-- BuildingDebugger auto-initializes

print("⚙️ Settings UI ready...")
-- SettingsUI ready for use

print("🏘️ Initializing Plot UI...")
local PlotUI = require(script:WaitForChild("PlotUI"))

print("🔊 Initializing Sound Controller...")
local SoundController = require(script:WaitForChild("SoundController"))

-- Sound Controller is auto-initialized, now we can use it for contextual sounds

-- Client state
local ClientState = {
	selectedBuildingType = nil,
	buildingMode = false,
	buildingRotation = 0, -- Building rotation in degrees
	playerData = {},
	buildings = {},
	camera = workspace.CurrentCamera,
	craftingWindowOpen = false
}

-- Make ClientState globally accessible for BuildingUI
_G.ClientState = ClientState

-- Forward declarations
local updateStatusIndicator

-- Notification system
local NotificationQueue = {}
local ActiveNotifications = {}

-- Create notification UI
local function createNotification(notificationType, message)
	local notification = Instance.new("Frame")
	notification.Name = "Notification"
	notification.Size = UDim2.new(0, 350, 0, 80)
	notification.Position = UDim2.new(1, -370, 0, 20 + (#ActiveNotifications * 90))
	notification.BackgroundColor3 = notificationType == "Error" and Color3.new(0.8, 0.2, 0.2) or
									notificationType == "Success" and Color3.new(0.2, 0.8, 0.2) or
									Color3.new(0.2, 0.6, 0.8)
	notification.BorderSizePixel = 0
	notification.ClipsDescendants = true

	-- Add corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = notification

	-- Add shadow effect
	local shadow = Instance.new("Frame")
	shadow.Name = "Shadow"
	shadow.Size = UDim2.new(1, 4, 1, 4)
	shadow.Position = UDim2.new(0, -2, 0, 2)
	shadow.BackgroundColor3 = Color3.new(0, 0, 0)
	shadow.BackgroundTransparency = 0.7
	shadow.ZIndex = notification.ZIndex - 1
	shadow.Parent = notification

	local shadowCorner = Instance.new("UICorner")
	shadowCorner.CornerRadius = UDim.new(0, 8)
	shadowCorner.Parent = shadow

	-- Icon
	local icon = Instance.new("TextLabel")
	icon.Name = "Icon"
	icon.Size = UDim2.new(0, 40, 0, 40)
	icon.Position = UDim2.new(0, 10, 0.5, -20)
	icon.BackgroundTransparency = 1
	icon.Text = notificationType == "Error" and "❌" or
				notificationType == "Success" and "✅" or
				"ℹ️"
	icon.TextColor3 = Color3.new(1, 1, 1)
	icon.TextScaled = true
	icon.Font = Enum.Font.SourceSansBold
	icon.Parent = notification

	-- Title
	local title = Instance.new("TextLabel")
	title.Name = "Title"
	title.Size = UDim2.new(1, -60, 0, 25)
	title.Position = UDim2.new(0, 55, 0, 8)
	title.BackgroundTransparency = 1
	title.Text = notificationType
	title.TextColor3 = Color3.new(1, 1, 1)
	title.TextScaled = true
	title.Font = Enum.Font.SourceSansBold
	title.TextXAlignment = Enum.TextXAlignment.Left
	title.Parent = notification

	-- Message
	local messageLabel = Instance.new("TextLabel")
	messageLabel.Name = "Message"
	messageLabel.Size = UDim2.new(1, -60, 0, 40)
	messageLabel.Position = UDim2.new(0, 55, 0, 30)
	messageLabel.BackgroundTransparency = 1
	messageLabel.Text = message
	messageLabel.TextColor3 = Color3.new(0.9, 0.9, 0.9)
	messageLabel.TextScaled = true
	messageLabel.Font = Enum.Font.SourceSans
	messageLabel.TextXAlignment = Enum.TextXAlignment.Left
	messageLabel.TextWrapped = true
	messageLabel.Parent = notification

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 20, 0, 20)
	closeButton.Position = UDim2.new(1, -25, 0, 5)
	closeButton.BackgroundTransparency = 1
	closeButton.Text = "×"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = notification

	return notification, closeButton
end

-- Hide notification function (declare first)
local function hideNotification(notification)
	if not notification or not notification.Parent then return end

	-- Use AnimationSystem for smooth slide out
	local targetPos = UDim2.new(1, 0, notification.Position.Y.Scale, notification.Position.Y.Offset)

	local slideTween = TweenService:Create(notification, TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In), {
		Position = targetPos
	})

	slideTween.Completed:Connect(function()
		-- Remove from active notifications
		for i, activeNotif in ipairs(ActiveNotifications) do
			if activeNotif == notification then
				table.remove(ActiveNotifications, i)
				break
			end
		end

		-- Reposition remaining notifications with smooth animation
		for i, activeNotif in ipairs(ActiveNotifications) do
			local newPos = UDim2.new(1, -370, 0, 20 + ((i-1) * 90))
			TweenService:Create(activeNotif, TweenInfo.new(0.2, Enum.EasingStyle.Quad), {
				Position = newPos
			}):Play()
		end

		notification:Destroy()
	end)

	slideTween:Play()
end

-- Show notification with enhanced animations
local function showNotification(notificationType, message)
	local ui = playerGui:FindFirstChild("UrbanSimUI")
	if not ui then return end

	local notification, closeButton = createNotification(notificationType, message)
	notification.Parent = ui

	-- Add to active notifications
	table.insert(ActiveNotifications, notification)

		-- Enhanced slide in animation using AnimationSystem
	local targetPos = notification.Position
	notification.Position = UDim2.new(1, 0, targetPos.Y.Scale, targetPos.Y.Offset)

	-- Ensure notification is visible before animating
	notification.Visible = true
	notification.BackgroundTransparency = 0

	local slideInTween = TweenService:Create(notification, TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
		Position = targetPos
	})

	slideInTween:Play()

	-- Auto-hide after 5 seconds
	local autoHideTimer = task.delay(5, function()
		hideNotification(notification)
	end)

	-- Close button functionality with animation
	closeButton.MouseButton1Click:Connect(function()
		task.cancel(autoHideTimer)
		-- Animate button press
		TweenService:Create(closeButton, TweenInfo.new(0.1), {Size = UDim2.new(0, 18, 0, 18)}):Play()
		task.wait(0.1)
		hideNotification(notification)
	end)

	-- Enhanced hover effects
	closeButton.MouseEnter:Connect(function()
		TweenService:Create(closeButton, TweenInfo.new(0.2), {
			BackgroundTransparency = 0.8,
			BackgroundColor3 = Color3.new(1, 1, 1),
			Size = UDim2.new(0, 22, 0, 22)
		}):Play()
	end)

	closeButton.MouseLeave:Connect(function()
		TweenService:Create(closeButton, TweenInfo.new(0.2), {
			BackgroundTransparency = 1,
			Size = UDim2.new(0, 20, 0, 20)
		}):Play()
	end)
end



-- UI Creation
local function createMainUI()
	local screenGui = Instance.new("ScreenGui")
	screenGui.Name = "UrbanSimUI"
	screenGui.ResetOnSpawn = false
	screenGui.Parent = playerGui

	-- Main Frame
	local mainFrame = Instance.new("Frame")
	mainFrame.Name = "MainFrame"
	mainFrame.Size = UDim2.new(1, 0, 1, 0)
	mainFrame.BackgroundTransparency = 1
	mainFrame.Parent = screenGui

	-- Top Bar (Stats) - Mobile-responsive
	local topBar = Instance.new("Frame")
	topBar.Name = "TopBar"

	-- Mobile-responsive TopBar height
	local isMobile = UserInputService.TouchEnabled and not UserInputService.KeyboardEnabled
	local topBarHeight = isMobile and 80 or 60 -- Taller for mobile

	topBar.Size = UDim2.new(1, 0, 0, topBarHeight)
	topBar.Position = UDim2.new(0, 0, 0, 0)
	topBar.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	topBar.BorderSizePixel = 0
	topBar.Parent = mainFrame

	-- Currency displays with mobile-responsive styling (including water and electricity)
	local currencies = {"Pieces", "Cash", "XP", "Population", "Level", "Energy", "Water"}
	local currencyIcons = {"💰", "💎", "⭐", "👥", "🏆", "⚡", "💧"}

	-- Mobile-responsive currency frame sizing (adjusted for 7 frames)
	local frameWidth = isMobile and 85 or 100
	local frameSpacing = isMobile and 95 or 110
	local frameMargin = isMobile and 3 or 5

	for i, currency in ipairs(currencies) do
		local currencyFrame = Instance.new("Frame")
		currencyFrame.Name = currency .. "Frame"
		currencyFrame.Size = UDim2.new(0, frameWidth, 1, -frameMargin)
		currencyFrame.Position = UDim2.new(0, (i-1) * frameSpacing + frameMargin, 0, frameMargin/2)
		currencyFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
		currencyFrame.BorderSizePixel = 0
		currencyFrame.Parent = topBar

		-- Add corner radius
		local currencyCorner = Instance.new("UICorner")
		currencyCorner.CornerRadius = UDim.new(0, 8)
		currencyCorner.Parent = currencyFrame

		-- Add gradient
		local gradient = Instance.new("UIGradient")
		gradient.Color = ColorSequence.new{
			ColorSequenceKeypoint.new(0, Color3.new(0.2, 0.2, 0.25)),
			ColorSequenceKeypoint.new(1, Color3.new(0.1, 0.1, 0.15))
		}
		gradient.Rotation = 90
		gradient.Parent = currencyFrame

		-- Currency icon
		local currencyIcon = Instance.new("TextLabel")
		currencyIcon.Name = currency .. "Icon"
		currencyIcon.Size = UDim2.new(0, 25, 1, 0)
		currencyIcon.Position = UDim2.new(0, 5, 0, 0)
		currencyIcon.BackgroundTransparency = 1
		currencyIcon.Text = currencyIcons[i]
		currencyIcon.TextColor3 = Color3.new(1, 1, 1)
		currencyIcon.TextScaled = true
		currencyIcon.Font = Enum.Font.SourceSansBold
		currencyIcon.Parent = currencyFrame

		-- Currency label
		local currencyLabel = Instance.new("TextLabel")
		currencyLabel.Name = currency .. "Label"
		currencyLabel.Size = UDim2.new(1, -30, 1, 0)
		currencyLabel.Position = UDim2.new(0, 30, 0, 0)
		currencyLabel.BackgroundTransparency = 1
		currencyLabel.Text = "0"
		currencyLabel.TextColor3 = Color3.new(1, 1, 1)
		currencyLabel.TextScaled = true
		currencyLabel.Font = Enum.Font.SourceSansBold
		currencyLabel.TextXAlignment = Enum.TextXAlignment.Left
		currencyLabel.Parent = currencyFrame

		-- Add hover effect
		currencyFrame.MouseEnter:Connect(function()
			TweenService:Create(currencyFrame, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
			}):Play()
		end)

		currencyFrame.MouseLeave:Connect(function()
			TweenService:Create(currencyFrame, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
			}):Play()
		end)
	end

	-- Old building button removed - using enhanced BuildingUI instead

	-- Crafting Menu Button - positioned to avoid currency frames overlap
	local craftingMenuButton = Instance.new("TextButton")
	craftingMenuButton.Name = "CraftingMenuButton"
	craftingMenuButton.Size = UDim2.new(0, 100, 0, 40)
	craftingMenuButton.Position = UDim2.new(1, -330, 0, 10) -- Moved left to avoid currency frames (3 * 140 + margin)
	craftingMenuButton.BackgroundColor3 = Color3.new(0.6, 0.4, 0.2)
	craftingMenuButton.Text = "Craft (C)"
	craftingMenuButton.TextColor3 = Color3.new(1, 1, 1)
	craftingMenuButton.TextScaled = true
	craftingMenuButton.Font = Enum.Font.SourceSansBold
	craftingMenuButton.Parent = topBar

	-- Add corner radius to crafting button
	local craftingCorner = Instance.new("UICorner")
	craftingCorner.CornerRadius = UDim.new(0, 6)
	craftingCorner.Parent = craftingMenuButton

	-- Status indicator for building mode
	local statusIndicator = Instance.new("TextLabel")
	statusIndicator.Name = "StatusIndicator"
	statusIndicator.Size = UDim2.new(0, 200, 0, 30)
	statusIndicator.Position = UDim2.new(0.5, -100, 1, -35)
	statusIndicator.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	statusIndicator.BackgroundTransparency = 0.3
	statusIndicator.Text = ""
	statusIndicator.TextColor3 = Color3.new(1, 1, 1)
	statusIndicator.TextScaled = true
	statusIndicator.Font = Enum.Font.SourceSansBold
	statusIndicator.Visible = false
	statusIndicator.Parent = mainFrame

	local statusCorner = Instance.new("UICorner")
	statusCorner.CornerRadius = UDim.new(0, 8)
	statusCorner.Parent = statusIndicator

	-- Old building menu removed - using enhanced BuildingUI instead



	return screenGui
end

-- Update UI with player data from leaderstats
local function updateUI(data)
	local ui = playerGui:FindFirstChild("UrbanSimUI")
	if not ui then return end

	local topBar = ui.MainFrame.TopBar

	-- Update currency displays using leaderstats
	local leaderstats = player:FindFirstChild("leaderstats")
	if leaderstats then
		-- Update from leaderstats
		local currencies = {"Pieces", "Cash", "XP", "Population", "Level"}
		for _, currency in ipairs(currencies) do
			local currencyFrame = topBar:FindFirstChild(currency .. "Frame")
			if currencyFrame then
				local label = currencyFrame:FindFirstChild(currency .. "Label")
				local leaderstatValue = leaderstats:FindFirstChild(currency)

				if label and leaderstatValue then
					-- Format large numbers
					local value = leaderstatValue.Value
					local formattedValue = value

					if value >= 1000000 then
						formattedValue = string.format("%.1fM", value / 1000000)
					elseif value >= 1000 then
						formattedValue = string.format("%.1fK", value / 1000)
					end

					label.Text = formattedValue
					print("💰 Updated", currency, "to", formattedValue)
				end
			end
		end
	else
		-- Fallback to data parameter if leaderstats not available
		local currencies = {"Pieces", "Cash", "XP", "Population", "Level"}
		for _, currency in ipairs(currencies) do
			local currencyFrame = topBar:FindFirstChild(currency .. "Frame")
			if currencyFrame then
				local label = currencyFrame:FindFirstChild(currency .. "Label")
				if label and data and data[currency] then
					local value = data[currency]
					local formattedValue = value

					if value >= 1000000 then
						formattedValue = string.format("%.1fM", value / 1000000)
					elseif value >= 1000 then
						formattedValue = string.format("%.1fK", value / 1000)
					end

					label.Text = formattedValue
					print("💰 Updated", currency, "to", formattedValue, "(from data)")
				end
			end
		end
	end
end

-- Update status indicator function with animations
updateStatusIndicator = function()
	local ui = playerGui:FindFirstChild("UrbanSimUI")
	if not ui then return end

	local statusIndicator = ui.MainFrame.StatusIndicator

	if ClientState.buildingMode and ClientState.selectedBuildingType then
		statusIndicator.Text = "Building Mode: " .. ClientState.selectedBuildingType:gsub("_", " ") .. " (ESC to cancel)"

		if not statusIndicator.Visible then
			statusIndicator.Visible = true
			-- Animate in
			statusIndicator.BackgroundTransparency = 1
			TweenService:Create(statusIndicator, TweenInfo.new(0.3, Enum.EasingStyle.Quad), {
				BackgroundTransparency = 0.3
			}):Play()
		end
	else
		if statusIndicator.Visible then
			-- Animate out
			TweenService:Create(statusIndicator, TweenInfo.new(0.2, Enum.EasingStyle.Quad), {
				BackgroundTransparency = 1
			}).Completed:Connect(function()
				statusIndicator.Visible = false
			end)
		end
	end
end

-- Building preview system
local currentPreview = nil

-- Load actual building model for preview
local function loadActualBuildingModel(buildingType)
	print("🔍 Loading actual building model for preview:", buildingType)

	-- Try ReplicatedStorage first
	local replicatedModels = ReplicatedStorage:FindFirstChild("BuildingModels")
	if replicatedModels then
		-- Try exact match first
		local model = replicatedModels:FindFirstChild(buildingType)
		if model and model:IsA("Model") then
			local clonedModel = model:Clone()
			print("✅ Loaded actual model from ReplicatedStorage:", buildingType)
			return clonedModel
		end

		-- Try case-insensitive search
		for _, child in pairs(replicatedModels:GetChildren()) do
			if child:IsA("Model") and child.Name:lower() == buildingType:lower() then
				local clonedModel = child:Clone()
				print("✅ Loaded case-insensitive model from ReplicatedStorage:", child.Name, "for", buildingType)
				return clonedModel
			end
		end
	end

	-- Try ServerStorage (if accessible)
	local serverModels = game:GetService("ServerStorage"):FindFirstChild("BuildingModels")
	if serverModels then
		local model = serverModels:FindFirstChild(buildingType)
		if model and model:IsA("Model") then
			local clonedModel = model:Clone()
			print("✅ Loaded actual model from ServerStorage:", buildingType)
			return clonedModel
		end
	end

	print("❌ No actual model found for:", buildingType, "- will use fallback")
	return nil
end

local function createBuildingPreview(buildingType, position)
	-- Remove existing preview
	if currentPreview then
		currentPreview:Destroy()
		currentPreview = nil
	end

	-- Get building config for proper size
	local buildingConfig = Config.BUILDINGS[buildingType]
	local buildingSize = buildingConfig and buildingConfig.Size or {4, 4, 4}

	-- Try to load actual building model first
	local preview = loadActualBuildingModel(buildingType)

	if not preview then
		-- Fallback to enhanced preview model
		preview = Instance.new("Model")
		preview.Name = "BuildingPreview"

		local part = Instance.new("Part")
		part.Name = "PreviewPart"
		part.Anchored = true
		part.CanCollide = false
		part.Transparency = 0.5
		part.Size = Vector3.new(buildingSize[1], buildingSize[2], buildingSize[3])
		part.Position = position + Vector3.new(0, part.Size.Y/2, 0)
		part.Color = Color3.new(0.5, 0.8, 1) -- Light blue preview color

		-- Apply rotation
		part.CFrame = CFrame.new(part.Position) * CFrame.Angles(0, math.rad(ClientState.buildingRotation), 0)
		part.Parent = preview
	else
		-- Configure real model for preview
		preview.Name = "BuildingPreview"

		-- Make all parts semi-transparent and non-collidable
		for _, descendant in pairs(preview:GetDescendants()) do
			if descendant:IsA("BasePart") then
				descendant.CanCollide = false
				descendant.Anchored = true
				descendant.Transparency = math.min(descendant.Transparency + 0.5, 0.8)
				-- Tint with preview color
				descendant.Color = descendant.Color:lerp(Color3.new(0.5, 0.8, 1), 0.3)
			end
		end

		-- Position and rotate the model
		if preview.PrimaryPart then
			preview:SetPrimaryPartCFrame(CFrame.new(position + Vector3.new(0, buildingSize[2]/2, 0)) * CFrame.Angles(0, math.rad(ClientState.buildingRotation), 0))
		else
			-- Move all parts if no PrimaryPart
			local modelCenter = Vector3.new(0, 0, 0)
			local partCount = 0
			for _, part in pairs(preview:GetChildren()) do
				if part:IsA("BasePart") then
					modelCenter = modelCenter + part.Position
					partCount = partCount + 1
				end
			end
			if partCount > 0 then
				modelCenter = modelCenter / partCount
				local offset = position + Vector3.new(0, buildingSize[2]/2, 0) - modelCenter
				for _, part in pairs(preview:GetChildren()) do
					if part:IsA("BasePart") then
						part.Position = part.Position + offset
						part.CFrame = part.CFrame * CFrame.Angles(0, math.rad(ClientState.buildingRotation), 0)
					end
				end
			end
		end
	end

	preview.Parent = workspace

	-- Add outline effect
	local selectionBox = Instance.new("SelectionBox")
	selectionBox.Adornee = part
	selectionBox.Color3 = Color3.new(0, 1, 0) -- Green for valid placement
	selectionBox.LineThickness = 0.2
	selectionBox.Transparency = 0.3
	selectionBox.Parent = part

	-- Add rotation indicator
	local rotationIndicator = Instance.new("Part")
	rotationIndicator.Name = "RotationIndicator"
	rotationIndicator.Anchored = true
	rotationIndicator.CanCollide = false
	rotationIndicator.Transparency = 0.7
	rotationIndicator.Size = Vector3.new(0.5, buildingSize[2] + 1, 0.5)
	rotationIndicator.Position = part.Position + part.CFrame.LookVector * (buildingSize[3]/2 + 1)
	rotationIndicator.Color = Color3.new(1, 1, 0) -- Yellow indicator
	rotationIndicator.Parent = preview

	currentPreview = preview
	return preview
end

local function updatePreviewColor(isValid)
	if currentPreview then
		local part = currentPreview:FindFirstChild("PreviewPart")
		local selectionBox = part and part:FindFirstChild("SelectionBox")

		if part and selectionBox then
			if isValid then
				part.Color = Color3.new(0.5, 1, 0.5) -- Light green for valid
				selectionBox.Color3 = Color3.new(0, 1, 0) -- Green outline
			else
				part.Color = Color3.new(1, 0.5, 0.5) -- Light red for invalid
				selectionBox.Color3 = Color3.new(1, 0, 0) -- Red outline
			end
		end
	end
end

-- Building placement state
local lastClickTime = 0
local CLICK_COOLDOWN = 0.5 -- Prevent spam clicking

-- Handle building placement
local function handleBuildingPlacement()
	if not ClientState.buildingMode or not ClientState.selectedBuildingType then
		-- Clean up preview if not in building mode
		if currentPreview then
			currentPreview:Destroy()
			currentPreview = nil
		end
		return
	end

	local mouse = player:GetMouse()
	local hit = mouse.Hit

	if hit then
		local position = hit.Position
		local gridPosition = BuildingSystem.WorldToGrid(position)
		local worldPosition = BuildingSystem.GridToWorld(gridPosition)

		-- Create or update building preview
		createBuildingPreview(ClientState.selectedBuildingType, worldPosition)

		-- Check if placement is valid (with comprehensive validation)
		local canPlace = false
		local canBuild = false
		local placeReason = ""
		local buildReason = ""

		-- Check placement validity
		local success1, result1 = pcall(function()
			if RemoteFunctions.CanPlaceBuilding then
				return RemoteFunctions.CanPlaceBuilding:InvokeServer(
					ClientState.selectedBuildingType,
					worldPosition
				)
			else
				warn("⚠️ RemoteFunctions.CanPlaceBuilding not found!")
				return false
			end
		end)

		-- Check building requirements (resources, level, etc.)
		local success2, result2 = pcall(function()
			if RemoteFunctions.CanBuild then
				return RemoteFunctions.CanBuild:InvokeServer(ClientState.selectedBuildingType)
			else
				warn("⚠️ RemoteFunctions.CanBuild not found!")
				return {canBuild = false, reason = "RemoteFunction not available"}
			end
		end)

		if success1 then
			canPlace = result1
		else
			warn("Failed to check building placement:", result1)
			canPlace = false
			placeReason = tostring(result1)
		end

		if success2 then
			if type(result2) == "table" then
				canBuild = result2.canBuild or false
				buildReason = result2.reason or ""
			else
				canBuild = result2 or false
			end
		else
			warn("Failed to check building requirements:", result2)
			canBuild = false
			buildReason = tostring(result2)
		end

		-- Only show green if BOTH placement and requirements are valid
		local finalCanPlace = canPlace and canBuild
		updatePreviewColor(finalCanPlace)

		-- Debug output
		if not finalCanPlace then
			local debugMsg = "🔍 Preview Debug: "
			if not canPlace then debugMsg = debugMsg .. "Placement: " .. placeReason .. " " end
			if not canBuild then debugMsg = debugMsg .. "Requirements: " .. buildReason end
			print(debugMsg)
		end
	end
end

-- Handle clicks/touches for building placement (mobile and desktop)
local function handleBuildingClick(input)
	if not ClientState.buildingMode or not ClientState.selectedBuildingType then
		return
	end

	local currentTime = tick()
	if currentTime - lastClickTime < CLICK_COOLDOWN then
		return -- Prevent spam clicking
	end

	local hit
	if input and input.UserInputType == Enum.UserInputType.Touch then
		-- Mobile touch input - use raycast from touch position
		local camera = workspace.CurrentCamera
		local ray = camera:ScreenPointToRay(input.Position.X, input.Position.Y)
		local raycastParams = RaycastParams.new()
		raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
		raycastParams.FilterDescendantsInstances = {camera, player.Character}

		local raycastResult = workspace:Raycast(ray.Origin, ray.Direction * 1000, raycastParams)
		if raycastResult then
			hit = CFrame.new(raycastResult.Position)
		end
	else
		-- Desktop mouse input
		local mouse = player:GetMouse()
		hit = mouse.Hit
	end

	if hit then
		local position = hit.Position
		local gridPosition = BuildingSystem.WorldToGrid(position)
		local worldPosition = BuildingSystem.GridToWorld(gridPosition)

		print("🏗️ Attempting to place building:", ClientState.selectedBuildingType, "at", worldPosition)

		-- Check placement validity
		local success1, canPlace = pcall(function()
			if RemoteFunctions.CanPlaceBuilding then
				return RemoteFunctions.CanPlaceBuilding:InvokeServer(
					ClientState.selectedBuildingType,
					worldPosition
				)
			else
				warn("⚠️ RemoteFunctions.CanPlaceBuilding not found!")
				return false
			end
		end)

		-- Check building requirements
		local success2, buildResult = pcall(function()
			if RemoteFunctions.CanBuild then
				return RemoteFunctions.CanBuild:InvokeServer(ClientState.selectedBuildingType)
			else
				warn("⚠️ RemoteFunctions.CanBuild not found!")
				return {canBuild = false, reason = "RemoteFunction not available"}
			end
		end)

		local canBuild = false
		local buildReason = ""
		if success2 and buildResult then
			canBuild = buildResult.canBuild or false
			buildReason = buildResult.reason or ""
		end

		print("🏗️ Placement check:", canPlace, "Build check:", canBuild, buildReason)

		if success1 and canPlace and canBuild then
			-- Place the building with rotation
			local placeSuccess, placeResult = pcall(function()
				RemoteEvents.PlaceBuilding:FireServer(ClientState.selectedBuildingType, worldPosition, ClientState.buildingRotation)
			end)

			if placeSuccess then
				ClientState.buildingMode = false
				ClientState.selectedBuildingType = nil
				ClientState.buildingRotation = 0 -- Reset rotation
				updateStatusIndicator()
				showNotification("Success", "Building placed successfully!")
				SoundController.PlayContextualSound("BUILDING_PLACE")
				lastClickTime = currentTime
				print("🏗️ Building placed successfully!")
			else
				warn("Failed to place building:", placeResult)
				showNotification("Error", "Failed to place building!")
				SoundController.PlayContextualSound("ERROR")
			end
		else
			local errorMsg = "Cannot place building here!"
			if not canPlace then
				errorMsg = "Invalid placement location!"
			elseif not canBuild then
				errorMsg = buildReason or "Cannot build this building!"
			end
			showNotification("Error", errorMsg)
			SoundController.PlayContextualSound("ERROR")
			print("🏗️ Cannot place building:", errorMsg)
		end
	end
end

-- Initialize client
local function initializeClient()
	-- Create UI
	local ui = createMainUI()

	-- Setup UI events
	local craftingMenuButton = ui.MainFrame.TopBar.CraftingMenuButton

	craftingMenuButton.MouseButton1Click:Connect(function()
		if ClientState.craftingWindowOpen then
			CraftingUI.CloseCraftingWindow()
			ClientState.craftingWindowOpen = false
			SoundController.PlayContextualSound("WINDOW_CLOSE")
		else
			CraftingUI.ShowCraftingWindow()
			ClientState.craftingWindowOpen = true
			SoundController.PlayContextualSound("WINDOW_OPEN")
			showNotification("Info", "Advanced crafting system opened!")
		end
	end)

	-- Crafting system is now handled by CraftingUI module

	-- Initialize additional UI modules after main UI is created
	print("🎁 Initializing Daily Rewards UI...")
	DailyRewardsUI.Initialize()

	print("🛒 Initializing Gamepass Shop UI...")
	GamepassShopUI.Initialize()

	print("🏗️ Initializing Enhanced Building UI...")
	BuildingUI.Initialize()

	print("📱 Initializing Mobile Building Controls...")
	MobileBuildingControls.Initialize()

	print("🏘️ Initializing Plot UI...")
	-- PlotUI auto-initializes, but we ensure it's properly integrated
	task.spawn(function()
		-- Wait for main UI to be fully loaded
		task.wait(1)
		if PlotUI and PlotUI.Initialize then
			PlotUI.Initialize()
		end
	end)

	print("⚙️ Settings UI ready for use...")
	-- SettingsUI will be created when needed

	-- Setup leaderstats monitoring
	local function setupLeaderstatsMonitoring()
		local leaderstats = player:WaitForChild("leaderstats", 10)
		if leaderstats then
			print("💰 Leaderstats found, setting up monitoring...")

			-- Initial update
			updateUI()

			-- Monitor changes to leaderstats
			local currencies = {"Pieces", "Cash", "XP", "Population", "Level"}
			for _, currency in ipairs(currencies) do
				local leaderstatValue = leaderstats:FindFirstChild(currency)
				if leaderstatValue then
					leaderstatValue.Changed:Connect(function()
						updateUI()
					end)
					print("💰 Monitoring", currency, "changes")
				else
					warn("💰 Leaderstat not found:", currency)
				end
			end
		else
			warn("💰 Leaderstats not found, using fallback data")
			-- Request initial player data as fallback
			task.wait(2) -- Wait for server to initialize
			local success, initialData = pcall(function()
				return RemoteFunctions.GetPlayerData:InvokeServer()
			end)

			if success and initialData then
				ClientState.playerData = initialData
				updateUI(initialData)
			else
				warn("💰 Failed to get player data:", initialData)
			end
		end
	end

	-- Setup leaderstats monitoring
	task.spawn(setupLeaderstatsMonitoring)
end

-- Remote event handlers
RemoteEvents.CurrencyUpdated.OnClientEvent:Connect(function(newData)
	ClientState.playerData = newData
	updateUI(newData)
end)

RemoteEvents.BuildingPlaced.OnClientEvent:Connect(function(buildingData)
	print("Building placed:", buildingData.Type)
	-- Update local building list
	ClientState.buildings[buildingData.Id] = buildingData
end)

RemoteEvents.ShowNotification.OnClientEvent:Connect(function(notificationType, message)
	showNotification(notificationType, message)
end)

-- Handle building placement input
RunService.Heartbeat:Connect(handleBuildingPlacement)

-- Handle input for building placement (mobile and desktop)
UserInputService.InputBegan:Connect(function(input, gameProcessed)
	if gameProcessed then return end

	-- Handle building placement clicks (mouse and touch)
	if input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
		handleBuildingClick(input)
	end

	-- Handle keyboard input (desktop only)
	if input.KeyCode == Enum.KeyCode.Q then
		-- Cancel building mode with Q key
		if ClientState.buildingMode then
			ClientState.buildingMode = false
			ClientState.selectedBuildingType = nil
			ClientState.buildingRotation = 0
			updateStatusIndicator()
			showNotification("Info", "Building placement cancelled")
		end
	elseif input.KeyCode == Enum.KeyCode.R then
		-- Rotate building clockwise with R key
		if ClientState.buildingMode then
			ClientState.buildingRotation = (ClientState.buildingRotation + 90) % 360
			showNotification("Info", "Building rotated: " .. ClientState.buildingRotation .. "°")
		end
	elseif input.KeyCode == Enum.KeyCode.E then
		-- Rotate building counter-clockwise with E key
		if ClientState.buildingMode then
			ClientState.buildingRotation = (ClientState.buildingRotation - 90) % 360
			if ClientState.buildingRotation < 0 then
				ClientState.buildingRotation = ClientState.buildingRotation + 360
			end
			showNotification("Info", "Building rotated: " .. ClientState.buildingRotation .. "°")
		end
	-- B key removed - old building system replaced with enhanced BuildingUI
	elseif input.KeyCode == Enum.KeyCode.C then
		-- Toggle crafting window with C key
		if ClientState.craftingWindowOpen then
			CraftingUI.CloseCraftingWindow()
			ClientState.craftingWindowOpen = false
			SoundController.PlayContextualSound("WINDOW_CLOSE")
		else
			CraftingUI.ShowCraftingWindow()
			ClientState.craftingWindowOpen = true
			SoundController.PlayContextualSound("WINDOW_OPEN")
			showNotification("Info", "🏭 Advanced crafting system opened!")
		end
	end
end)

-- Initialize when player spawns
player.CharacterAdded:Connect(function()
	task.wait(1)
	initializeClient()
end)

-- Initialize immediately if character already exists
if player.Character then
	initializeClient()
end

print("✅ UrbanSim Client Initialized!")