--[[
	Enhanced Building UI Module
	Professional building interface with ViewportFrames, 3D previews, and grid layouts
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local GuiService = game:GetService("GuiService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Get shared modules
local Shared = ReplicatedStorage:WaitForChild("Shared")
local Config = require(Shared:WaitForChild("Config"))

-- Get RemoteEvents
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))
local RemoteFunctions = require(Assets:WaitForChild("RemoteFunctions"))

local BuildingUI = {}

-- UI State
local buildingWindow = nil
local currentCategory = "Residential"
local selectedBuilding = nil
local isOpen = false

-- Mobile detection and responsive settings
local isMobile = UserInputService.TouchEnabled and not UserInputService.KeyboardEnabled
local isTablet = UserInputService.TouchEnabled and UserInputService.KeyboardEnabled
local isTouchDevice = UserInputService.TouchEnabled

print("📱 Device detection - Mobile:", isMobile, "Tablet:", isTablet, "Touch:", isTouchDevice)

-- Mobile-responsive utility function to get consistent window dimensions
local function getWindowDimensions()
	local screenSize = workspace.CurrentCamera.ViewportSize
	-- Use GuiService.TopbarInset for safe area handling instead of GetGuiInsets
	local topbarInset = GuiService.TopbarInset
	local availableWidth = screenSize.X
	local availableHeight = screenSize.Y - topbarInset.Height

	local windowWidth, windowHeight, containerHeight

	if isMobile then
		-- Mobile: Use almost full screen for better usability
		windowWidth = math.min(availableWidth * 0.98, availableWidth - 10)
		windowHeight = math.min(availableHeight * 0.95, availableHeight - 20)
		containerHeight = math.max(140, math.min(180, windowHeight * 0.2)) -- Larger for touch
	elseif isTablet then
		-- Tablet: Balanced approach
		windowWidth = math.min(900, math.max(600, availableWidth * 0.8))
		windowHeight = math.min(700, math.max(500, availableHeight * 0.85))
		containerHeight = math.max(110, math.min(150, windowHeight * 0.16))
	else
		-- Desktop: Original sizing
		windowWidth = math.min(1200, math.max(800, availableWidth * 0.85))
		windowHeight = math.min(800, math.max(600, availableHeight * 0.8))
		containerHeight = math.max(100, math.min(140, windowHeight * 0.15))
	end

	return windowWidth, windowHeight, containerHeight
end

-- Animation settings
local TWEEN_INFO = {
	SlideIn = TweenInfo.new(0.4, Enum.EasingStyle.Quart, Enum.EasingDirection.Out),
	SlideOut = TweenInfo.new(0.3, Enum.EasingStyle.Quart, Enum.EasingDirection.In),
	Bounce = TweenInfo.new(0.2, Enum.EasingStyle.Elastic, Enum.EasingDirection.Out),
	Glow = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true)
}

-- Enhanced building categories with comprehensive building lists
local BUILDING_CATEGORIES = {
	{
		Name = "Residential",
		Icon = "🏠",
		Color = Color3.new(0.8, 0.6, 0.4),
		Description = "Housing for your citizens",
		Buildings = {"HOUSE_SMALL", "HOUSE_MEDIUM", "APARTMENT", "MANSION", "SKYSCRAPER"}
	},
	{
		Name = "Commercial",
		Icon = "🏢",
		Color = Color3.new(0.2, 0.6, 0.8),
		Description = "Businesses and commerce",
		Buildings = {"SHOP_SMALL", "SHOP_MEDIUM", "SHOP_LARGE", "OFFICE", "MALL", "BANK", "RESTAURANT", "HOTEL", "GAS_STATION"}
	},
	{
		Name = "Industrial",
		Icon = "🏭",
		Color = Color3.new(0.6, 0.6, 0.6),
		Description = "Production and manufacturing",
		Buildings = {"FACTORY_SMALL", "FACTORY_LARGE", "WAREHOUSE", "METAL_FACTORY", "PLASTIC_FACTORY", "WOOD_FACTORY", "ELECTRONICS_FACTORY", "TECH_FACTORY"}
	},
	{
		Name = "Service",
		Icon = "🚑",
		Color = Color3.new(0.8, 0.8, 0.2),
		Description = "Essential city services",
		Buildings = {"POLICE_STATION", "FIRE_STATION", "HOSPITAL", "SCHOOL", "LIBRARY", "UNIVERSITY"}
	},
	{
		Name = "Utility",
		Icon = "⚡",
		Color = Color3.new(0.5, 0.5, 0.8),
		Description = "Infrastructure and utilities",
		Buildings = {"POWER_PLANT", "WATER_PLANT", "WATER_TREATMENT", "SOLAR_PLANT", "WIND_TURBINE", "GARBAGE_DUMP", "RECYCLING_CENTER", "WASTE_MANAGEMENT"}
	},
	{
		Name = "Decoration",
		Icon = "🌳",
		Color = Color3.new(0.2, 0.8, 0.2),
		Description = "Parks and beautification",
		Buildings = {"PARK_SMALL", "PARK_MEDIUM", "PARK_LARGE", "FOUNTAIN", "STATUE", "PLAYGROUND", "GARDEN", "MONUMENT"}
	},
	{
		Name = "Special",
		Icon = "🏛️",
		Color = Color3.new(0.8, 0.2, 0.8),
		Description = "Unique landmark buildings",
		Buildings = {"CITY_HALL", "AIRPORT", "STADIUM", "LIGHTHOUSE"}
	}
}

-- Create building button in main UI
function BuildingUI.CreateBuildingButton()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then 
		warn("🏗️ UrbanSimUI not found!")
		return 
	end

	-- Enhanced building button - mobile-responsive sizing and positioning
	local buildingButton = Instance.new("TextButton")
	buildingButton.Name = "EnhancedBuildingButton"

	-- Mobile-responsive button sizing
	local buttonWidth, buttonHeight
	if isMobile then
		buttonWidth, buttonHeight = 150, 70 -- Larger for touch
	elseif isTablet then
		buttonWidth, buttonHeight = 140, 65
	else
		buttonWidth, buttonHeight = 130, 55 -- Desktop size
	end

	buildingButton.Size = UDim2.new(0, buttonWidth, 0, buttonHeight)

	-- Mobile-responsive positioning
	if isMobile then
		buildingButton.Position = UDim2.new(1, -buttonWidth - 10, 0, 280) -- More margin for mobile
	else
		buildingButton.Position = UDim2.new(1, -buttonWidth - 20, 0, 250) -- Desktop positioning
	end
	buildingButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.2)
	buildingButton.Text = "🏗️ Build"
	buildingButton.TextColor3 = Color3.new(1, 1, 1)

	-- Mobile-responsive text size
	if isMobile then
		buildingButton.TextSize = 22 -- Larger for mobile
	elseif isTablet then
		buildingButton.TextSize = 20
	else
		buildingButton.TextSize = 18 -- Desktop size
	end

	buildingButton.Font = Enum.Font.SourceSansBold
	buildingButton.ZIndex = 10 -- Ensure it's above other elements
	buildingButton.Parent = screenGui

	-- Add corner radius and gradient
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = buildingButton

	local gradient = Instance.new("UIGradient")
	gradient.Color = ColorSequence.new{
		ColorSequenceKeypoint.new(0, Color3.new(0.25, 0.7, 0.25)),
		ColorSequenceKeypoint.new(1, Color3.new(0.15, 0.5, 0.15))
	}
	gradient.Rotation = 45
	gradient.Parent = buildingButton

	-- Add glow effect
	local glow = Instance.new("ImageLabel")
	glow.Name = "Glow"
	glow.Size = UDim2.new(1, 20, 1, 20)
	glow.Position = UDim2.new(0, -10, 0, -10)
	glow.BackgroundTransparency = 1
	glow.Image = "rbxasset://textures/ui/GuiImagePlaceholder.png"
	glow.ImageColor3 = Color3.new(0.2, 0.8, 0.2)
	glow.ImageTransparency = 0.7
	glow.ZIndex = buildingButton.ZIndex - 1
	glow.Parent = buildingButton

	local glowCorner = Instance.new("UICorner")
	glowCorner.CornerRadius = UDim.new(0, 16)
	glowCorner.Parent = glow

	-- Button functionality
	buildingButton.MouseButton1Click:Connect(function()
		if isOpen then
			BuildingUI.CloseBuildingWindow()
		else
			BuildingUI.ShowBuildingWindow()
		end
	end)

	-- Touch and hover effects (mobile-responsive)
	if not isTouchDevice then
		-- Desktop hover effects
		buildingButton.MouseEnter:Connect(function()
			TweenService:Create(buildingButton, TWEEN_INFO.Bounce, {
				Size = UDim2.new(0, buttonWidth + 5, 0, buttonHeight + 2)
			}):Play()
			TweenService:Create(glow, TWEEN_INFO.Glow, {
				ImageTransparency = 0.3
			}):Play()
		end)

		buildingButton.MouseLeave:Connect(function()
			TweenService:Create(buildingButton, TWEEN_INFO.Bounce, {
				Size = UDim2.new(0, buttonWidth, 0, buttonHeight)
			}):Play()
			TweenService:Create(glow, TWEEN_INFO.Glow, {
				ImageTransparency = 0.7
			}):Play()
		end)
	else
		-- Touch device: Use button press effects instead
		buildingButton.MouseButton1Down:Connect(function()
			TweenService:Create(buildingButton, TweenInfo.new(0.1), {
				Size = UDim2.new(0, buttonWidth - 5, 0, buttonHeight - 2),
				BackgroundColor3 = Color3.new(0.15, 0.5, 0.15)
			}):Play()
		end)

		buildingButton.MouseButton1Up:Connect(function()
			TweenService:Create(buildingButton, TweenInfo.new(0.1), {
				Size = UDim2.new(0, buttonWidth, 0, buttonHeight),
				BackgroundColor3 = Color3.new(0.2, 0.6, 0.2)
			}):Play()
		end)
	end

	print("🏗️ Enhanced building button created")
	return buildingButton
end

-- Create main building window
function BuildingUI.CreateBuildingWindow()
	if buildingWindow then
		buildingWindow:Destroy()
	end

	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end

	print("🏗️ Creating enhanced building window...")

	-- Main window frame with responsive sizing
	buildingWindow = Instance.new("Frame")
	buildingWindow.Name = "EnhancedBuildingWindow"

	-- Responsive sizing based on screen size
	local windowWidth, windowHeight = getWindowDimensions()

	buildingWindow.Size = UDim2.new(0, windowWidth, 0, windowHeight)
	buildingWindow.Position = UDim2.new(0.5, -windowWidth/2, 0.5, -windowHeight/2)
	buildingWindow.BackgroundColor3 = Color3.new(0.05, 0.05, 0.1)
	buildingWindow.BorderSizePixel = 0
	buildingWindow.Visible = false
	buildingWindow.ZIndex = 5 -- Ensure window is above other UI elements
	buildingWindow.Parent = screenGui

	print("🏗️ Building window created with size:", windowWidth, "x", windowHeight)

	-- Add corner radius and gradient
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 16)
	corner.Parent = buildingWindow

	local gradient = Instance.new("UIGradient")
	gradient.Color = ColorSequence.new{
		ColorSequenceKeypoint.new(0, Color3.new(0.1, 0.05, 0.2)),
		ColorSequenceKeypoint.new(1, Color3.new(0.05, 0.05, 0.1))
	}
	gradient.Rotation = 45
	gradient.Parent = buildingWindow

	-- Create window sections
	BuildingUI.CreateTitleBar()
	BuildingUI.CreateCategoryTabs()
	BuildingUI.CreateBuildingGrid()
	BuildingUI.CreateBuildingDetails()

	return buildingWindow
end

-- Create title bar
function BuildingUI.CreateTitleBar()
	local titleBar = Instance.new("Frame")
	titleBar.Name = "TitleBar"
	titleBar.Size = UDim2.new(1, 0, 0, 60)
	titleBar.Position = UDim2.new(0, 0, 0, 0)
	titleBar.BackgroundColor3 = Color3.new(0.15, 0.1, 0.25)
	titleBar.BorderSizePixel = 0
	titleBar.ZIndex = 6
	titleBar.Parent = buildingWindow

	-- Only round top corners for title bar
	local titleCorner = Instance.new("UICorner")
	titleCorner.CornerRadius = UDim.new(0, 16)
	titleCorner.Parent = titleBar

	-- Title text
	local titleText = Instance.new("TextLabel")
	titleText.Name = "TitleText"
	titleText.Size = UDim2.new(1, -120, 1, 0)
	titleText.Position = UDim2.new(0, 20, 0, 0)
	titleText.BackgroundTransparency = 1
	titleText.Text = "🏗️ Enhanced Building System"
	titleText.TextColor3 = Color3.new(1, 1, 1)
	titleText.TextSize = 24
	titleText.Font = Enum.Font.SourceSansBold
	titleText.TextXAlignment = Enum.TextXAlignment.Left
	titleText.TextYAlignment = Enum.TextYAlignment.Center
	titleText.ZIndex = 7
	titleText.Parent = titleBar

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 40, 0, 40)
	closeButton.Position = UDim2.new(1, -50, 0, 10)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	closeButton.Text = "×"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextSize = 24
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.ZIndex = 7
	closeButton.Parent = titleBar

	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 8)
	closeCorner.Parent = closeButton

	closeButton.MouseButton1Click:Connect(function()
		BuildingUI.CloseBuildingWindow()
	end)
end

-- Create category tabs with responsive design and search
function BuildingUI.CreateCategoryTabs()
	-- Create main container for tabs and search
	local tabsAndSearchContainer = Instance.new("Frame")
	tabsAndSearchContainer.Name = "TabsAndSearchContainer"

	-- Get window dimensions using utility function
	local windowWidth, _windowHeight, containerHeight = getWindowDimensions()

	tabsAndSearchContainer.Size = UDim2.new(1, -20, 0, containerHeight)
	tabsAndSearchContainer.Position = UDim2.new(0, 10, 0, 70) -- Just below title bar
	tabsAndSearchContainer.BackgroundTransparency = 1
	tabsAndSearchContainer.ZIndex = 6
	tabsAndSearchContainer.Parent = buildingWindow

	print("🏗️ Tabs container created with height:", containerHeight)

	-- Create search bar
	local searchContainer = Instance.new("Frame")
	searchContainer.Name = "SearchContainer"
	searchContainer.Size = UDim2.new(0.25, -5, 0, 40) -- Smaller to give more space to tabs
	searchContainer.Position = UDim2.new(0.75, 0, 0, 5) -- Right aligned
	searchContainer.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	searchContainer.BorderSizePixel = 0
	searchContainer.ZIndex = 7
	searchContainer.Parent = tabsAndSearchContainer

	local searchCorner = Instance.new("UICorner")
	searchCorner.CornerRadius = UDim.new(0, 8)
	searchCorner.Parent = searchContainer

	local searchBox = Instance.new("TextBox")
	searchBox.Name = "SearchBox"
	searchBox.Size = UDim2.new(1, -45, 1, -8)
	searchBox.Position = UDim2.new(0, 40, 0, 4)
	searchBox.BackgroundTransparency = 1
	searchBox.Text = ""
	searchBox.PlaceholderText = "Search buildings..."
	searchBox.TextColor3 = Color3.new(1, 1, 1)
	searchBox.PlaceholderColor3 = Color3.new(0.6, 0.6, 0.6)
	searchBox.TextSize = 14
	searchBox.Font = Enum.Font.SourceSans
	searchBox.TextXAlignment = Enum.TextXAlignment.Left
	searchBox.ZIndex = 8
	searchBox.Parent = searchContainer

	local searchIcon = Instance.new("TextLabel")
	searchIcon.Name = "SearchIcon"
	searchIcon.Size = UDim2.new(0, 35, 1, 0)
	searchIcon.Position = UDim2.new(0, 5, 0, 0)
	searchIcon.BackgroundTransparency = 1
	searchIcon.Text = "🔍"
	searchIcon.TextColor3 = Color3.new(0.6, 0.6, 0.6)
	searchIcon.TextSize = 16
	searchIcon.Font = Enum.Font.SourceSans
	searchIcon.TextXAlignment = Enum.TextXAlignment.Center
	searchIcon.TextYAlignment = Enum.TextYAlignment.Center
	searchIcon.ZIndex = 8
	searchIcon.Parent = searchContainer

	-- Create category tabs container
	local tabContainer = Instance.new("Frame")
	tabContainer.Name = "CategoryTabs"

	local tabHeight = containerHeight - 50 -- Leave space for search bar
	tabContainer.Size = UDim2.new(1, -10, 0, tabHeight) -- Use full width minus margins
	tabContainer.Position = UDim2.new(0, 5, 0, 50) -- Positioned below search
	tabContainer.BackgroundTransparency = 1
	tabContainer.ZIndex = 7
	tabContainer.Parent = tabsAndSearchContainer

	-- TabBackground removed for cleaner appearance

	local tabLayout = Instance.new("UIListLayout")
	tabLayout.FillDirection = Enum.FillDirection.Horizontal
	tabLayout.SortOrder = Enum.SortOrder.LayoutOrder
	tabLayout.Padding = UDim.new(0, 5) -- Reduced padding for more space
	tabLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
	tabLayout.VerticalAlignment = Enum.VerticalAlignment.Center
	tabLayout.Parent = tabContainer

	-- Calculate responsive tab width to fit all tabs
	local categoryCount = #BUILDING_CATEGORIES + 1 -- +1 for "All" tab
	local tabContainerWidth = windowWidth - 40 -- Full width minus margins
	local totalPadding = (categoryCount - 1) * 5 + 20 -- padding between tabs + container margins
	local availableWidth = tabContainerWidth - totalPadding
	local tabWidth = math.max(80, math.min(120, availableWidth / categoryCount)) -- Smaller max width

	print("🏗️ Creating", categoryCount, "tabs with width:", tabWidth, "height:", tabHeight)

	-- Add "All" category tab
	local allTab = Instance.new("TextButton")
	allTab.Name = "AllTab"
	allTab.Size = UDim2.new(0, tabWidth, 1, -8) -- Consistent margin
	allTab.BackgroundColor3 = currentCategory == "All" and Color3.new(0.2, 0.8, 0.2) or Color3.new(0.2, 0.2, 0.25)
	allTab.Text = "📋 All"
	allTab.TextColor3 = Color3.new(1, 1, 1)
	allTab.TextSize = math.max(10, math.min(14, tabWidth * 0.12)) -- Better text scaling
	allTab.Font = Enum.Font.SourceSansBold
	allTab.TextScaled = true -- Enable text scaling
	allTab.LayoutOrder = 0
	allTab.ZIndex = 8
	allTab.Parent = tabContainer

	local allTabCorner = Instance.new("UICorner")
	allTabCorner.CornerRadius = UDim.new(0, 8)
	allTabCorner.Parent = allTab

	-- Add border if "All" is the current category
	if currentCategory == "All" then
		local border = Instance.new("UIStroke")
		border.Color = Color3.new(1, 1, 1)
		border.Thickness = 2
		border.Transparency = 0.7
		border.Parent = allTab
	end

	-- All tab functionality
	allTab.MouseButton1Click:Connect(function()
		BuildingUI.SwitchCategory("All")
	end)

	-- Create tabs for each category
	for i, category in ipairs(BUILDING_CATEGORIES) do
		local tab = Instance.new("TextButton")
		tab.Name = category.Name .. "Tab"
		tab.Size = UDim2.new(0, tabWidth, 1, -8) -- Consistent margin
		tab.BackgroundColor3 = category.Name == currentCategory and category.Color or Color3.new(0.2, 0.2, 0.25)
		tab.Text = category.Icon .. " " .. category.Name
		tab.TextColor3 = Color3.new(1, 1, 1)
		tab.TextSize = math.max(10, math.min(14, tabWidth * 0.12)) -- Better text scaling
		tab.Font = Enum.Font.SourceSansBold
		tab.TextScaled = true -- Enable text scaling
		tab.LayoutOrder = i + 1 -- +1 because "All" tab is 0
		tab.ZIndex = 8
		tab.Parent = tabContainer

		local tabCorner = Instance.new("UICorner")
		tabCorner.CornerRadius = UDim.new(0, 8)
		tabCorner.Parent = tab

		-- Add border for active tab
		if category.Name == currentCategory then
			local border = Instance.new("UIStroke")
			border.Color = Color3.new(1, 1, 1)
			border.Thickness = 2
			border.Transparency = 0.7
			border.Parent = tab
			print("✅ Initial active tab set:", category.Name)
		end

		-- Tab functionality
		tab.MouseButton1Click:Connect(function()
			BuildingUI.SwitchCategory(category.Name)
		end)

		-- Enhanced hover effects
		tab.MouseEnter:Connect(function()
			if category.Name ~= currentCategory then
				TweenService:Create(tab, TweenInfo.new(0.15), {
					BackgroundColor3 = category.Color:lerp(Color3.new(1, 1, 1), 0.2),
					Size = UDim2.new(0, tabWidth + 2, 1, -6) -- Slight grow effect
				}):Play()
			end
		end)

		tab.MouseLeave:Connect(function()
			if category.Name ~= currentCategory then
				TweenService:Create(tab, TweenInfo.new(0.15), {
					BackgroundColor3 = Color3.new(0.2, 0.2, 0.25),
					Size = UDim2.new(0, tabWidth, 1, -8) -- Return to normal size
				}):Play()
			end
		end)

		print("🏗️ Created tab:", category.Name, "with size:", tabWidth, "x", tabHeight - 8)
	end

	-- Search functionality
	searchBox.Changed:Connect(function(property)
		if property == "Text" then
			local searchText = searchBox.Text:lower()
			if searchText == "" then
				-- Show current category when search is empty
				BuildingUI.LoadCategoryBuildings(currentCategory)
			else
				-- Search across all buildings
				BuildingUI.SearchBuildings(searchText)
			end
		end
	end)

	print("🏗️ Created category tabs with search functionality")
end

-- Create building grid
function BuildingUI.CreateBuildingGrid()
	local gridContainer = Instance.new("Frame")
	gridContainer.Name = "BuildingGrid"

	-- Calculate position based on tabs and search container height
	local _, _, containerHeight = getWindowDimensions()
	local gridY = 70 + containerHeight + 15 -- Title bar + tabs/search + margin

	gridContainer.Size = UDim2.new(0.65, -10, 1, -(gridY + 20)) -- Leave space at bottom
	gridContainer.Position = UDim2.new(0, 10, 0, gridY)
	gridContainer.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	gridContainer.BorderSizePixel = 0
	gridContainer.ZIndex = 6
	gridContainer.Parent = buildingWindow

	print("🏗️ Building grid positioned at Y:", gridY)

	local gridCorner = Instance.new("UICorner")
	gridCorner.CornerRadius = UDim.new(0, 12)
	gridCorner.Parent = gridContainer

	-- Scrolling frame for buildings
	local scrollFrame = Instance.new("ScrollingFrame")
	scrollFrame.Name = "BuildingScrollFrame"
	scrollFrame.Size = UDim2.new(1, -20, 1, -20)
	scrollFrame.Position = UDim2.new(0, 10, 0, 10)
	scrollFrame.BackgroundTransparency = 1
	scrollFrame.BorderSizePixel = 0
	scrollFrame.ScrollBarThickness = 8
	scrollFrame.ScrollBarImageColor3 = Color3.new(0.5, 0.5, 0.5)
	scrollFrame.CanvasSize = UDim2.new(0, 0, 0, 0) -- Initial canvas size
	scrollFrame.ScrollingDirection = Enum.ScrollingDirection.Y
	scrollFrame.VerticalScrollBarInset = Enum.ScrollBarInset.ScrollBar
	scrollFrame.ZIndex = 7
	scrollFrame.Parent = gridContainer

	-- Mobile-responsive grid layout for building cards
	local gridLayout = Instance.new("UIGridLayout")

	-- Calculate responsive card size with mobile optimization
	local windowWidth = getWindowDimensions()
	local gridWidth = windowWidth * 0.65 - 60 -- Account for actual grid container width

	local cardWidth, cardHeight, cellPadding, cardsPerRow
	if isMobile then
		-- Mobile: Larger cards, fewer per row for touch
		cardsPerRow = 2
		cardWidth = math.max(180, math.min(220, gridWidth / cardsPerRow - 20))
		cardHeight = math.max(300, cardWidth * 1.7) -- Extra height for touch targets
		cellPadding = 15
	elseif isTablet then
		-- Tablet: Medium cards
		cardsPerRow = 3
		cardWidth = math.max(170, math.min(210, gridWidth / cardsPerRow - 20))
		cardHeight = math.max(290, cardWidth * 1.65)
		cellPadding = 12
	else
		-- Desktop: Original sizing
		cardsPerRow = 3
		cardWidth = math.max(160, math.min(200, gridWidth / cardsPerRow - 20))
		cardHeight = math.max(280, cardWidth * 1.6)
		cellPadding = 10
	end

	gridLayout.CellSize = UDim2.new(0, cardWidth, 0, cardHeight)
	gridLayout.CellPadding = UDim2.new(0, cellPadding, 0, cellPadding)
	gridLayout.SortOrder = Enum.SortOrder.LayoutOrder
	gridLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
	gridLayout.VerticalAlignment = Enum.VerticalAlignment.Top
	gridLayout.Parent = scrollFrame

	print("🏗️ Grid layout created with card size:", cardWidth, "x", cardHeight)
	print("🏗️ ScrollFrame created with size:", scrollFrame.AbsoluteSize)

	-- Update canvas size when content changes
	gridLayout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
		local contentSize = gridLayout.AbsoluteContentSize
		scrollFrame.CanvasSize = UDim2.new(0, 0, 0, contentSize.Y + 20)
		print("🏗️ Updated canvas size to:", contentSize.Y + 20)
	end)
end

-- Create building details panel
function BuildingUI.CreateBuildingDetails()
	local detailsPanel = Instance.new("Frame")
	detailsPanel.Name = "BuildingDetails"

	-- Calculate position based on tabs and search container height (same as grid)
	local _, _, containerHeight = getWindowDimensions()
	local detailsY = 70 + containerHeight + 15 -- Title bar + tabs/search + margin

	detailsPanel.Size = UDim2.new(0.35, -15, 1, -(detailsY + 20)) -- Leave space at bottom
	detailsPanel.Position = UDim2.new(0.65, 5, 0, detailsY)
	detailsPanel.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	detailsPanel.BorderSizePixel = 0
	detailsPanel.ZIndex = 6
	detailsPanel.Parent = buildingWindow

	print("🏗️ Building details positioned at Y:", detailsY)

	local detailsCorner = Instance.new("UICorner")
	detailsCorner.CornerRadius = UDim.new(0, 12)
	detailsCorner.Parent = detailsPanel

	-- ViewportFrame for 3D preview
	local viewport = Instance.new("ViewportFrame")
	viewport.Name = "BuildingPreview"
	viewport.Size = UDim2.new(1, -20, 0, 180) -- Slightly smaller
	viewport.Position = UDim2.new(0, 10, 0, 10)
	viewport.BackgroundColor3 = Color3.new(0.05, 0.05, 0.1)
	viewport.BorderSizePixel = 0
	viewport.ZIndex = 7
	viewport.Parent = detailsPanel

	local viewportCorner = Instance.new("UICorner")
	viewportCorner.CornerRadius = UDim.new(0, 8)
	viewportCorner.Parent = viewport

	-- Camera for viewport
	local camera = Instance.new("Camera")
	camera.Parent = viewport
	viewport.CurrentCamera = camera

	-- Building info section with responsive sizing
	local infoSection = Instance.new("Frame")
	infoSection.Name = "BuildingInfo"

	-- Calculate responsive sizing based on window height
	local _, windowHeight, tabContainerHeight = getWindowDimensions()
	local detailsPanelHeight = windowHeight - (70 + tabContainerHeight + 15 + 20) -- Total available height
	local viewportHeight = 180
	local infoHeight = detailsPanelHeight - viewportHeight - 30 -- Leave margins

	infoSection.Size = UDim2.new(1, -20, 0, infoHeight)
	infoSection.Position = UDim2.new(0, 10, 0, viewportHeight + 20)
	infoSection.BackgroundTransparency = 1
	infoSection.ZIndex = 7
	infoSection.Parent = detailsPanel

	print("🏗️ Building info section sized:", infoHeight, "pixels")

	-- Building name
	local nameLabel = Instance.new("TextLabel")
	nameLabel.Name = "BuildingName"
	nameLabel.Size = UDim2.new(1, 0, 0, 30)
	nameLabel.BackgroundTransparency = 1
	nameLabel.Text = "Select a building"
	nameLabel.TextColor3 = Color3.new(1, 1, 1)
	nameLabel.TextSize = 18
	nameLabel.Font = Enum.Font.SourceSansBold
	nameLabel.TextXAlignment = Enum.TextXAlignment.Left
	nameLabel.TextYAlignment = Enum.TextYAlignment.Center
	nameLabel.ZIndex = 8
	nameLabel.Parent = infoSection

	-- Building description
	local descLabel = Instance.new("TextLabel")
	descLabel.Name = "BuildingDescription"
	descLabel.Size = UDim2.new(1, 0, 0, 50) -- Slightly smaller
	descLabel.Position = UDim2.new(0, 0, 0, 35)
	descLabel.BackgroundTransparency = 1
	descLabel.Text = "Choose a building from the grid to see details"
	descLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	descLabel.TextSize = 14
	descLabel.Font = Enum.Font.SourceSans
	descLabel.TextWrapped = true
	descLabel.TextXAlignment = Enum.TextXAlignment.Left
	descLabel.TextYAlignment = Enum.TextYAlignment.Top
	descLabel.ZIndex = 8
	descLabel.Parent = infoSection

	-- Enhanced cost section with better visibility
	local costSection = Instance.new("Frame")
	costSection.Name = "CostSection"

	-- Calculate responsive cost section height with more space
	local costHeight = math.max(80, math.min(120, infoHeight * 0.35)) -- 35% of info section for more space

	costSection.Size = UDim2.new(1, 0, 0, costHeight)
	costSection.Position = UDim2.new(0, 0, 0, 90) -- Below description
	costSection.BackgroundColor3 = Color3.new(0.18, 0.18, 0.25) -- Slightly lighter for visibility
	costSection.BorderSizePixel = 0
	costSection.ZIndex = 8
	costSection.Parent = infoSection

	local costCorner = Instance.new("UICorner")
	costCorner.CornerRadius = UDim.new(0, 8)
	costCorner.Parent = costSection

	-- Add subtle border for better definition
	local costBorder = Instance.new("UIStroke")
	costBorder.Color = Color3.new(0.3, 0.3, 0.4)
	costBorder.Thickness = 1
	costBorder.Transparency = 0.5
	costBorder.Parent = costSection

	print("🏗️ Enhanced cost section sized:", costHeight, "pixels")

	-- Enhanced build button with better positioning
	local buildButton = Instance.new("TextButton")
	buildButton.Name = "BuildButton"
	buildButton.Size = UDim2.new(1, 0, 0, 50) -- Larger for better visibility
	buildButton.Position = UDim2.new(0, 0, 1, -60) -- Better positioning
	buildButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
	buildButton.Text = "🏗️ Build Selected"
	buildButton.TextColor3 = Color3.new(1, 1, 1)
	buildButton.TextSize = 18 -- Larger text
	buildButton.Font = Enum.Font.SourceSansBold
	buildButton.ZIndex = 9 -- Higher Z-Index for interaction
	buildButton.Parent = infoSection

	local buildCorner = Instance.new("UICorner")
	buildCorner.CornerRadius = UDim.new(0, 10)
	buildCorner.Parent = buildButton

	-- Add button border for better definition
	local buildBorder = Instance.new("UIStroke")
	buildBorder.Color = Color3.new(0.3, 1, 0.3)
	buildBorder.Thickness = 2
	buildBorder.Transparency = 0.3
	buildBorder.Parent = buildButton

	-- Add hover effects for better UX
	buildButton.MouseEnter:Connect(function()
		TweenService:Create(buildButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.3, 0.9, 0.3)
		}):Play()
	end)

	buildButton.MouseLeave:Connect(function()
		TweenService:Create(buildButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
		}):Play()
	end)

	buildButton.MouseButton1Click:Connect(function()
		if selectedBuilding then
			-- Get building config
			local buildingConfig = Config.BUILDINGS[selectedBuilding]
			if not buildingConfig then
				print("❌ Building config not found for:", selectedBuilding)
				return
			end

			-- Enhanced resource checking with multiple fallback methods
			local canAfford = true
			local missingResources = {}

			if buildingConfig.Cost then
				-- Get player data from leaderstats (client-side safe method)
				local playerData = nil

				-- Get player data from leaderstats
				local leaderstats = Players.LocalPlayer:FindFirstChild("leaderstats")
				if leaderstats then
					playerData = {}
					for _, stat in pairs(leaderstats:GetChildren()) do
						if stat:IsA("IntValue") or stat:IsA("NumberValue") then
							playerData[stat.Name] = stat.Value
						end
					end
				end

				-- Fallback: Default values for testing if leaderstats not available
				if not playerData then
					print("⚠️ No leaderstats found, using default values for testing")
					playerData = {
						Cash = 10000,
						Pieces = 100,
						Energy = 1000,
						Water = 1000
					}
				end

				-- Check if player can afford each cost
				for currency, amount in pairs(buildingConfig.Cost) do
					local playerAmount = playerData[currency] or 0
					if playerAmount < amount then
						canAfford = false
						table.insert(missingResources, {
							currency = currency,
							needed = amount,
							have = playerAmount,
							missing = amount - playerAmount
						})
					end
				end
			end

			if canAfford then
				print("✅ Can afford " .. buildingConfig.Name .. ", starting placement...")
				BuildingUI.StartBuildingPlacement(selectedBuilding)
			else
				print("💰 Cannot afford " .. buildingConfig.Name .. "!")
				for _, resource in ipairs(missingResources) do
					print("  Need " .. resource.missing .. " more " .. resource.currency .. " (have " .. resource.have .. "/" .. resource.needed .. ")")
				end

				-- Update build button to show cost issue
				buildButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
				buildButton.Text = "💰 Can't Afford"

				-- Reset button after 2 seconds
				task.wait(2)
				buildButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
				buildButton.Text = "🏗️ Build Selected"
			end
		else
			print("⚠️ No building selected")
			buildButton.BackgroundColor3 = Color3.new(0.8, 0.4, 0.2)
			buildButton.Text = "⚠️ Select Building First"

			-- Reset button after 2 seconds
			task.wait(2)
			buildButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
			buildButton.Text = "🏗️ Build Selected"
		end
	end)
end

-- Show building window
function BuildingUI.ShowBuildingWindow()
	print("🏗️ ShowBuildingWindow called")

	if isOpen then
		print("🏗️ Window already open")
		return
	end
	isOpen = true

	if not buildingWindow then
		print("🏗️ Creating building window...")
		local success, result = pcall(function()
			return BuildingUI.CreateBuildingWindow()
		end)

		if not success then
			warn("🏗️ Error creating building window:", result)
			isOpen = false
			return
		end
	end

	if not buildingWindow then
		print("🏗️ ERROR: Failed to create building window!")
		isOpen = false
		return
	end

	-- Play window open sound
	pcall(function()
		local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
		if SoundController and SoundController.PlayContextualSound then
			SoundController.PlayContextualSound("WINDOW_OPEN")
		end
	end)

	print("🏗️ Making window visible and animating...")
	buildingWindow.Visible = true

	-- Get current window size for responsive animation
	local windowSize = buildingWindow.AbsoluteSize
	local windowWidth = windowSize.X
	local windowHeight = windowSize.Y

	-- Slide in animation from bottom
	buildingWindow.Position = UDim2.new(0.5, -windowWidth/2, 1, 0)
	local slideInTween = TweenService:Create(buildingWindow, TWEEN_INFO.SlideIn, {
		Position = UDim2.new(0.5, -windowWidth/2, 0.5, -windowHeight/2)
	})

	slideInTween:Play()

	-- Update tab appearances to ensure correct initial state
	BuildingUI.UpdateTabAppearances()

	-- Load current category
	print("🏗️ Loading category:", currentCategory)
	if currentCategory == "All" then
		BuildingUI.LoadAllBuildings()
	else
		BuildingUI.LoadCategoryBuildings(currentCategory)
	end
	print("🏗️ Building window opened successfully")
end

-- Close building window
function BuildingUI.CloseBuildingWindow()
	print("🏗️ CloseBuildingWindow called")

	if not isOpen or not buildingWindow then
		print("🏗️ Window not open or doesn't exist")
		return
	end
	isOpen = false

	-- Play window close sound
	pcall(function()
		local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
		if SoundController and SoundController.PlayContextualSound then
			SoundController.PlayContextualSound("WINDOW_CLOSE")
		end
	end)

	print("🏗️ Animating window close...")

	-- Get current window size for responsive animation
	local windowSize = buildingWindow.AbsoluteSize
	local windowWidth = windowSize.X

	local slideOutTween = TweenService:Create(buildingWindow, TWEEN_INFO.SlideOut, {
		Position = UDim2.new(0.5, -windowWidth/2, 1, 0)
	})

	slideOutTween.Completed:Connect(function()
		print("🏗️ Window hidden")
		buildingWindow.Visible = false
	end)

	slideOutTween:Play()
end

-- Switch category
function BuildingUI.SwitchCategory(categoryName)
	print("🏗️ Switching to category:", categoryName)
	currentCategory = categoryName

	-- Clear search box when switching categories
	local tabsAndSearchContainer = buildingWindow:FindFirstChild("TabsAndSearchContainer")
	if tabsAndSearchContainer then
		local searchContainer = tabsAndSearchContainer:FindFirstChild("SearchContainer")
		if searchContainer then
			local searchBox = searchContainer:FindFirstChild("SearchBox")
			if searchBox then
				searchBox.Text = ""
			end
		end
	end

	-- Update all tab appearances
	BuildingUI.UpdateTabAppearances()

	-- Load buildings for this category
	if categoryName == "All" then
		BuildingUI.LoadAllBuildings()
	else
		BuildingUI.LoadCategoryBuildings(categoryName)
	end
end

-- Update tab appearances based on current category
function BuildingUI.UpdateTabAppearances()
	local tabsAndSearchContainer = buildingWindow:FindFirstChild("TabsAndSearchContainer")
	if not tabsAndSearchContainer then return end

	local tabContainer = tabsAndSearchContainer:FindFirstChild("CategoryTabs")
	if not tabContainer then return end

	print("🎨 Updating tab appearances for category:", currentCategory)

	for _, tab in pairs(tabContainer:GetChildren()) do
		if tab:IsA("TextButton") then
			-- Remove existing border
			local existingBorder = tab:FindFirstChild("UIStroke")
			if existingBorder then
				existingBorder:Destroy()
			end

			-- Handle "All" tab
			if tab.Name == "AllTab" then
				if currentCategory == "All" then
					-- Active "All" tab
					tab.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
					local border = Instance.new("UIStroke")
					border.Color = Color3.new(1, 1, 1)
					border.Thickness = 2
					border.Transparency = 0.7
					border.Parent = tab
					print("✅ All tab set as active")
				else
					-- Inactive "All" tab
					tab.BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
				end
			else
				-- Handle category tabs
				local categoryName = tab.Name:gsub("Tab", "")
				local categoryData = nil

				-- Find category data
				for _, cat in ipairs(BUILDING_CATEGORIES) do
					if cat.Name == categoryName then
						categoryData = cat
						break
					end
				end

				if categoryData then
					if categoryName == currentCategory then
						-- Active category tab
						tab.BackgroundColor3 = categoryData.Color
						local border = Instance.new("UIStroke")
						border.Color = Color3.new(1, 1, 1)
						border.Thickness = 2
						border.Transparency = 0.7
						border.Parent = tab
						print("✅ " .. categoryName .. " tab set as active")
					else
						-- Inactive category tab
						tab.BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
					end
				end
			end
		end
	end
end

-- Load buildings for category
function BuildingUI.LoadCategoryBuildings(categoryName)
	print("🏗️ Loading buildings for category:", categoryName)

	-- Enhanced debugging for UI structure
	local buildingGrid = buildingWindow:FindFirstChild("BuildingGrid")
	if not buildingGrid then
		warn("🏗️ BuildingGrid not found!")
		print("🏗️ Available children in buildingWindow:")
		for _, child in pairs(buildingWindow:GetChildren()) do
			print("  -", child.Name, "(" .. child.ClassName .. ")")
		end
		return
	end

	local scrollFrame = buildingGrid:FindFirstChild("BuildingScrollFrame")
	if not scrollFrame then
		warn("🏗️ BuildingScrollFrame not found!")
		print("🏗️ Available children in BuildingGrid:")
		for _, child in pairs(buildingGrid:GetChildren()) do
			print("  -", child.Name, "(" .. child.ClassName .. ")")
		end
		return
	end

	print("✅ Found ScrollFrame with size:", scrollFrame.AbsoluteSize)
	print("✅ ScrollFrame visible:", scrollFrame.Visible)
	print("✅ ScrollFrame parent:", scrollFrame.Parent and scrollFrame.Parent.Name or "nil")

	-- Clear existing building cards
	local clearedCount = 0
	for _, child in ipairs(scrollFrame:GetChildren()) do
		if child:IsA("Frame") and child.Name:find("BuildingCard") then
			child:Destroy()
			clearedCount = clearedCount + 1
		end
	end
	print("🧹 Cleared", clearedCount, "existing building cards")

	-- Find category data
	local categoryData = nil
	for _, cat in ipairs(BUILDING_CATEGORIES) do
		if cat.Name == categoryName then
			categoryData = cat
			break
		end
	end

	if not categoryData then
		warn("🏗️ Category not found:", categoryName)
		print("🏗️ Available categories:")
		for _, cat in ipairs(BUILDING_CATEGORIES) do
			print("  -", cat.Name)
		end
		return
	end

	print("✅ Found category data for:", categoryName, "with", #categoryData.Buildings, "buildings")

	-- Create building cards
	local cardCount = 0
	for i, buildingType in ipairs(categoryData.Buildings) do
		local buildingConfig = Config.BUILDINGS[buildingType]
		if buildingConfig then
			local card = BuildingUI.CreateBuildingCard(buildingType, buildingConfig, scrollFrame, i)
			if card then
				cardCount = cardCount + 1
				print("✅ Created card for:", buildingConfig.Name, "- Size:", card.AbsoluteSize)
			else
				warn("❌ Failed to create card for:", buildingType)
			end
		else
			warn("🏗️ Building config not found for:", buildingType)
			-- Debug: Print all available buildings
			print("🔍 Available buildings in Config.BUILDINGS:")
			for availableBuildingType, _ in pairs(Config.BUILDINGS) do
				if availableBuildingType:find("FACTORY") then
					print("  - Factory building found:", availableBuildingType)
				end
			end
		end
	end

	print("🏗️ Created", cardCount, "building cards for", categoryName)

	-- Debug final state
	print("🔍 Final ScrollFrame children count:", #scrollFrame:GetChildren())
	local gridLayout = scrollFrame:FindFirstChildOfClass("UIGridLayout")
	if gridLayout then
		print("🔍 GridLayout AbsoluteContentSize:", gridLayout.AbsoluteContentSize)
		print("🔍 ScrollFrame CanvasSize:", scrollFrame.CanvasSize)
	end
end

-- Search buildings across all categories
function BuildingUI.SearchBuildings(searchText)
	print("🔍 Searching for:", searchText)

	local scrollFrame = buildingWindow:FindFirstChild("BuildingGrid"):FindFirstChild("BuildingScrollFrame")
	if not scrollFrame then
		warn("🔍 ScrollFrame not found!")
		return
	end

	-- Clear existing building cards
	for _, child in ipairs(scrollFrame:GetChildren()) do
		if child:IsA("Frame") and child.Name:find("BuildingCard") then
			child:Destroy()
		end
	end

	-- Search through all buildings
	local foundBuildings = {}
	for buildingType, buildingConfig in pairs(Config.BUILDINGS) do
		local name = buildingConfig.Name:lower()
		local type = (buildingConfig.Type or ""):lower()

		if name:find(searchText) or type:find(searchText) or buildingType:lower():find(searchText) then
			table.insert(foundBuildings, {type = buildingType, config = buildingConfig})
		end
	end

	-- Create cards for found buildings
	local cardCount = 0
	for i, building in ipairs(foundBuildings) do
		BuildingUI.CreateBuildingCard(building.type, building.config, scrollFrame, i)
		cardCount = cardCount + 1
		print("🔍 Found:", building.config.Name)
	end

	print("🔍 Found", cardCount, "buildings matching:", searchText)
end

-- Load all buildings from all categories
function BuildingUI.LoadAllBuildings()
	print("🏗️ Loading all buildings")

	local scrollFrame = buildingWindow:FindFirstChild("BuildingGrid"):FindFirstChild("BuildingScrollFrame")
	if not scrollFrame then
		warn("🏗️ ScrollFrame not found!")
		return
	end

	-- Clear existing building cards
	for _, child in ipairs(scrollFrame:GetChildren()) do
		if child:IsA("Frame") and child.Name:find("BuildingCard") then
			child:Destroy()
		end
	end

	-- Create building cards for all buildings
	local cardCount = 0
	local layoutOrder = 1

	for _, category in ipairs(BUILDING_CATEGORIES) do
		for _, buildingType in ipairs(category.Buildings) do
			local buildingConfig = Config.BUILDINGS[buildingType]
			if buildingConfig then
				BuildingUI.CreateBuildingCard(buildingType, buildingConfig, scrollFrame, layoutOrder)
				cardCount = cardCount + 1
				layoutOrder = layoutOrder + 1
				print("🏗️ Created card for:", buildingConfig.Name)
			else
				warn("🏗️ Building config not found for:", buildingType)
				-- Debug: Print all available buildings when error occurs
				if buildingType:find("FACTORY") then
					print("🔍 Missing factory building:", buildingType)
					print("🔍 Available factory buildings in Config.BUILDINGS:")
					for availableBuildingType, _ in pairs(Config.BUILDINGS) do
						if availableBuildingType:find("FACTORY") then
							print("  - Found:", availableBuildingType)
						end
					end
				end
			end
		end
	end

	print("🏗️ Created", cardCount, "building cards for all categories")
end

-- Create building card with ViewportFrame
function BuildingUI.CreateBuildingCard(buildingType, buildingConfig, parent, layoutOrder)
	-- Get grid layout to determine card size
	local gridLayout = parent:FindFirstChildOfClass("UIGridLayout")
	local cardSize = gridLayout and gridLayout.CellSize or UDim2.new(0, 180, 0, 240)

	local card = Instance.new("Frame")
	card.Name = buildingType .. "BuildingCard"
	card.Size = cardSize -- Explicitly set size for visibility
	card.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
	card.BorderSizePixel = 0
	card.LayoutOrder = layoutOrder
	card.Visible = true -- Ensure visibility
	card.ZIndex = 8 -- Ensure it's above other elements
	card.Parent = parent

	print("🏗️ Creating building card:", buildingType, "with size:", cardSize)

	local cardCorner = Instance.new("UICorner")
	cardCorner.CornerRadius = UDim.new(0, 12)
	cardCorner.Parent = card

	-- Gradient background
	local cardGradient = Instance.new("UIGradient")
	cardGradient.Color = ColorSequence.new{
		ColorSequenceKeypoint.new(0, Color3.new(0.2, 0.2, 0.25)),
		ColorSequenceKeypoint.new(1, Color3.new(0.1, 0.1, 0.15))
	}
	cardGradient.Rotation = 45
	cardGradient.Parent = card

	-- ViewportFrame for 3D building preview
	local viewport = Instance.new("ViewportFrame")
	viewport.Name = "BuildingViewport"
	viewport.Size = UDim2.new(1, -10, 0, 130) -- Increased height for better 3D view
	viewport.Position = UDim2.new(0, 5, 0, 5)
	viewport.BackgroundColor3 = Color3.new(0.05, 0.05, 0.1)
	viewport.BorderSizePixel = 0
	viewport.ZIndex = 9 -- Ensure viewport is visible
	viewport.Parent = card

	local viewportCorner = Instance.new("UICorner")
	viewportCorner.CornerRadius = UDim.new(0, 8)
	viewportCorner.Parent = viewport

	-- Create 3D model preview
	BuildingUI.CreateBuildingPreview(viewport, buildingType, buildingConfig)

	-- Building name with icon
	local nameLabel = Instance.new("TextLabel")
	nameLabel.Name = "BuildingName"
	nameLabel.Size = UDim2.new(1, -10, 0, 25)
	nameLabel.Position = UDim2.new(0, 5, 0, 140) -- Adjusted for larger viewport
	nameLabel.BackgroundTransparency = 1
	nameLabel.Text = (buildingConfig.Icon or "🏗️") .. " " .. (buildingConfig.Name or buildingType)
	nameLabel.TextColor3 = Color3.new(1, 1, 1)

	-- Mobile-responsive text size
	if isMobile then
		nameLabel.TextSize = 18 -- Larger for mobile
	elseif isTablet then
		nameLabel.TextSize = 16
	else
		nameLabel.TextSize = 14 -- Desktop size
	end

	nameLabel.Font = Enum.Font.SourceSansBold
	nameLabel.TextWrapped = true
	nameLabel.TextXAlignment = Enum.TextXAlignment.Center
	nameLabel.ZIndex = 9 -- Ensure text is visible
	nameLabel.Parent = card

	-- Enhanced cost display with multiple currencies
	local costLabel = Instance.new("TextLabel")
	costLabel.Name = "CostLabel"
	costLabel.Size = UDim2.new(1, -10, 0, 20)
	costLabel.Position = UDim2.new(0, 5, 0, 170) -- Adjusted spacing
	costLabel.BackgroundTransparency = 1

	-- Build cost string with all currencies and proper icons
	local costText = ""
	if buildingConfig.Cost then
		local costParts = {}
		for currency, amount in pairs(buildingConfig.Cost) do
			if currency == "Pieces" then
				table.insert(costParts, "🧩" .. amount .. " Pieces")
			elseif currency == "Cash" then
				table.insert(costParts, "💰$" .. amount)
			elseif currency == "Energy" then
				table.insert(costParts, "⚡" .. amount .. " Energy")
			elseif currency == "Water" then
				table.insert(costParts, "💧" .. amount .. " Water")
			else
				table.insert(costParts, "💰" .. amount .. " " .. currency)
			end
		end
		costText = table.concat(costParts, " ")
	else
		costText = "🆓 Free"
	end

	costLabel.Text = costText
	costLabel.TextColor3 = Color3.new(0.9, 0.9, 0.6)

	-- Mobile-responsive cost text size
	if isMobile then
		costLabel.TextSize = 16 -- Larger for mobile
	elseif isTablet then
		costLabel.TextSize = 14
	else
		costLabel.TextSize = 12 -- Desktop size
	end

	costLabel.Font = Enum.Font.SourceSans
	costLabel.TextWrapped = true
	costLabel.TextXAlignment = Enum.TextXAlignment.Center
	costLabel.ZIndex = 9 -- Ensure text is visible
	costLabel.Parent = card

	-- Building stats display
	local statsLabel = Instance.new("TextLabel")
	statsLabel.Name = "StatsLabel"
	statsLabel.Size = UDim2.new(1, -10, 0, 20)
	statsLabel.Position = UDim2.new(0, 5, 0, 195) -- Adjusted spacing
	statsLabel.BackgroundTransparency = 1

	-- Build stats string
	local statsText = ""
	local statsParts = {}

	if buildingConfig.Population then
		table.insert(statsParts, "👥" .. buildingConfig.Population)
	end
	if buildingConfig.EnergyProduction then
		table.insert(statsParts, "⚡+" .. buildingConfig.EnergyProduction)
	end
	if buildingConfig.EnergyConsumption then
		table.insert(statsParts, "⚡-" .. buildingConfig.EnergyConsumption)
	end
	if buildingConfig.WaterProduction then
		table.insert(statsParts, "💧+" .. buildingConfig.WaterProduction)
	end
	if buildingConfig.WaterConsumption then
		table.insert(statsParts, "💧-" .. buildingConfig.WaterConsumption)
	end

	if #statsParts > 0 then
		statsText = table.concat(statsParts, " ")
	else
		statsText = "🏗️ Decoration"
	end

	statsLabel.Text = statsText
	statsLabel.TextColor3 = Color3.new(0.7, 0.9, 0.7)
	statsLabel.TextSize = 11
	statsLabel.Font = Enum.Font.SourceSans
	statsLabel.TextWrapped = true
	statsLabel.TextXAlignment = Enum.TextXAlignment.Center
	statsLabel.ZIndex = 9 -- Ensure text is visible
	statsLabel.Parent = card

	-- Level requirement with player level check
	local levelLabel = Instance.new("TextLabel")
	levelLabel.Name = "LevelLabel"
	levelLabel.Size = UDim2.new(1, -10, 0, 20)
	levelLabel.Position = UDim2.new(0, 5, 0, 220) -- Adjusted spacing
	levelLabel.BackgroundTransparency = 1

	-- Get player level from leaderstats
	local playerLevel = 1
	local leaderstats = Players.LocalPlayer:FindFirstChild("leaderstats")
	if leaderstats then
		local levelStat = leaderstats:FindFirstChild("Level")
		if levelStat then
			playerLevel = levelStat.Value
		end
	end

	local requiredLevel = buildingConfig.UnlockLevel or 1
	local canBuild = playerLevel >= requiredLevel

	if canBuild then
		levelLabel.Text = "🔓 Level " .. requiredLevel .. " (Unlocked)"
		levelLabel.TextColor3 = Color3.new(0.2, 0.8, 0.2) -- Green
	else
		levelLabel.Text = "🔒 Level " .. requiredLevel .. " (Need " .. (requiredLevel - playerLevel) .. " more)"
		levelLabel.TextColor3 = Color3.new(0.8, 0.2, 0.2) -- Red
	end

	levelLabel.TextSize = 12
	levelLabel.Font = Enum.Font.SourceSans
	levelLabel.TextXAlignment = Enum.TextXAlignment.Left
	levelLabel.ZIndex = 9 -- Ensure text is visible
	levelLabel.Parent = card

	-- Enhanced selection functionality with level check and mobile responsiveness
	local selectButton = Instance.new("TextButton")
	selectButton.Name = "SelectButton"

	-- Mobile-responsive button sizing
	local buttonHeight = isMobile and 40 or (isTablet and 35 or 30)
	selectButton.Size = UDim2.new(0.7, -5, 0, buttonHeight) -- Smaller width to make room for remove button
	selectButton.Position = UDim2.new(0, 5, 1, -(buttonHeight + 5)) -- Positioned at bottom

	-- Mobile-responsive button text size
	if isMobile then
		selectButton.TextSize = 18 -- Larger for mobile touch
	elseif isTablet then
		selectButton.TextSize = 16
	else
		selectButton.TextSize = 14 -- Desktop size
	end

	selectButton.Font = Enum.Font.SourceSansBold
	selectButton.ZIndex = 10 -- Ensure button is clickable
	selectButton.Parent = card

	local selectCorner = Instance.new("UICorner")
	selectCorner.CornerRadius = UDim.new(0, 6)
	selectCorner.Parent = selectButton

	-- Check if player can build this
	local currentPlayerLevel = 1
	local playerLeaderstats = Players.LocalPlayer:FindFirstChild("leaderstats")
	if playerLeaderstats then
		local levelStat = playerLeaderstats:FindFirstChild("Level")
		if levelStat then
			currentPlayerLevel = levelStat.Value
		end
	end

	local buildingRequiredLevel = buildingConfig.UnlockLevel or 1
	local playerCanBuild = currentPlayerLevel >= buildingRequiredLevel

	if playerCanBuild then
		selectButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
		selectButton.Text = "Select"
		selectButton.TextColor3 = Color3.new(1, 1, 1)

		-- Button functionality
		selectButton.MouseButton1Click:Connect(function()
			BuildingUI.SelectBuilding(buildingType, buildingConfig)
		end)
	else
		selectButton.BackgroundColor3 = Color3.new(0.4, 0.4, 0.4)
		selectButton.Text = "Locked"
		selectButton.TextColor3 = Color3.new(0.7, 0.7, 0.7)

		-- Show level requirement when clicked
		selectButton.MouseButton1Click:Connect(function()
			print("🔒 Building locked! Need level", buildingRequiredLevel, "(currently level " .. currentPlayerLevel .. ")")
		end)
	end

	-- Add remove building button (for placed buildings) with mobile responsiveness
	local removeButton = Instance.new("TextButton")
	removeButton.Name = "RemoveButton"
	removeButton.Size = UDim2.new(0.25, -5, 0, buttonHeight) -- Match select button height
	removeButton.Position = UDim2.new(0.7, 5, 1, -(buttonHeight + 5)) -- Next to select button
	removeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	removeButton.Text = "🗑️"
	removeButton.TextColor3 = Color3.new(1, 1, 1)

	-- Mobile-responsive remove button text size
	if isMobile then
		removeButton.TextSize = 18 -- Larger for mobile touch
	elseif isTablet then
		removeButton.TextSize = 16
	else
		removeButton.TextSize = 14 -- Desktop size
	end

	removeButton.Font = Enum.Font.SourceSansBold
	removeButton.ZIndex = 10 -- Ensure button is clickable
	removeButton.Parent = card

	local removeCorner = Instance.new("UICorner")
	removeCorner.CornerRadius = UDim.new(0, 4)
	removeCorner.Parent = removeButton

	-- Remove button functionality
	removeButton.MouseButton1Click:Connect(function()
		BuildingUI.StartBuildingRemoval(buildingType)
	end)

	-- Hover effects
	card.MouseEnter:Connect(function()
		TweenService:Create(card, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
		}):Play()
	end)

	card.MouseLeave:Connect(function()
		TweenService:Create(card, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
		}):Play()
	end)

	return card
end

-- Enhanced 3D building preview with model loading and fixed visibility
function BuildingUI.CreateBuildingPreview(viewport, buildingType, buildingConfig)
	-- Clear any existing content
	viewport:ClearAllChildren()

	-- Create camera with proper settings
	local camera = Instance.new("Camera")
	camera.Parent = viewport
	viewport.CurrentCamera = camera
	camera.FieldOfView = 50
	camera.CameraType = Enum.CameraType.Scriptable

	-- Try to load actual building model first
	local model = BuildingUI.LoadBuildingModelForPreview(buildingType, buildingConfig)
	if not model then
		-- Create enhanced fallback model if no actual model found
		model = BuildingUI.CreateEnhancedFallbackPreview(buildingType, buildingConfig)
	end

	model.Name = buildingType .. "Preview"
	model.Parent = viewport

	-- Ensure all parts are visible and properly configured
	for _, part in pairs(model:GetDescendants()) do
		if part:IsA("BasePart") then
			part.CanCollide = false
			part.Anchored = true
			part.CastShadow = false -- Disable shadows for better performance
			-- Ensure parts are visible
			if part.Transparency > 0.9 then
				part.Transparency = 0.1
			end
			-- Ensure parts have proper colors
			if part.Color == Color3.new(0, 0, 0) then
				part.Color = Color3.new(0.5, 0.5, 0.5) -- Default gray
			end
		end
	end

	-- Add multiple light sources for better visibility
	local lighting = Instance.new("PointLight")
	lighting.Brightness = 5 -- Very bright for viewport
	lighting.Range = 1000
	lighting.Color = Color3.new(1, 1, 1)

	-- Add ambient lighting
	local ambientLight = Instance.new("PointLight")
	ambientLight.Brightness = 3
	ambientLight.Range = 500
	ambientLight.Color = Color3.new(0.9, 0.9, 1)

	-- Add lighting to the main part
	if model.PrimaryPart then
		lighting.Parent = model.PrimaryPart
		ambientLight.Parent = model.PrimaryPart
	else
		local mainPart = model:FindFirstChildOfClass("BasePart")
		if mainPart then
			lighting.Parent = mainPart
			ambientLight.Parent = mainPart
		end
	end

	-- Get model bounds for camera positioning
	local modelSize = Vector3.new(4, 4, 4) -- Default size

	-- Try to get actual model size
	local success, _cf, size = pcall(function()
		return model:GetBoundingBox()
	end)

	if success and size.Magnitude > 0 then
		modelSize = size
	else
		-- Use config size as fallback
		local configSize = buildingConfig.Size or {4, 4, 4}
		modelSize = Vector3.new(configSize[1], configSize[2], configSize[3])
	end

	-- Position camera to view the building optimally
	local maxSize = math.max(modelSize.X, modelSize.Y, modelSize.Z)
	local distance = math.max(8, maxSize * 2.5) -- Ensure minimum distance

	-- Position camera at an angle that shows the building well
	local cameraPosition = Vector3.new(distance * 0.7, distance * 0.8, distance * 0.7)
	local lookAtPosition = Vector3.new(0, modelSize.Y * 0.4, 0) -- Look slightly below center

	camera.CFrame = CFrame.lookAt(cameraPosition, lookAtPosition)

	print("🎥 Camera positioned at:", cameraPosition, "looking at:", lookAtPosition)

	-- Add smooth rotation animation
	local rotationConnection
	rotationConnection = RunService.Heartbeat:Connect(function()
		if model.Parent then
			-- Rotate the entire model smoothly
			local rotation = tick() * 0.4 -- Smooth rotation
			if model.PrimaryPart then
				model:SetPrimaryPartCFrame(CFrame.new(0, modelSize.Y/2, 0) * CFrame.Angles(0, rotation, 0))
			else
				-- Rotate individual parts if no PrimaryPart
				for _, part in pairs(model:GetChildren()) do
					if part:IsA("BasePart") then
						local originalPos = part:GetAttribute("OriginalPosition")
						if not originalPos then
							part:SetAttribute("OriginalPosition", part.Position)
							originalPos = part.Position
						end
						-- Simple rotation around Y axis
						local rotatedPos = CFrame.new(0, 0, 0) * CFrame.Angles(0, rotation, 0) * CFrame.new(originalPos)
						part.Position = rotatedPos.Position
						part.Rotation = Vector3.new(0, math.deg(rotation), 0)
					end
				end
			end
		else
			rotationConnection:Disconnect()
		end
	end)

	-- Debug viewport and model visibility
	print("🔍 Viewport debug info for", buildingType .. ":")
	print("  - Viewport size:", viewport.AbsoluteSize)
	print("  - Viewport visible:", viewport.Visible)
	print("  - Model children count:", #model:GetChildren())
	print("  - Model has PrimaryPart:", model.PrimaryPart ~= nil)

	-- List all parts in the model for debugging
	for i, child in ipairs(model:GetChildren()) do
		if child:IsA("BasePart") then
			print("  - Part " .. i .. ":", child.Name, "Size:", child.Size, "Transparency:", child.Transparency, "Color:", child.Color)
		end
	end

	print("🏗️ Created enhanced preview for:", buildingType, "with size:", modelSize)
end

-- Enhanced model loading with comprehensive search and debugging
function BuildingUI.LoadBuildingModelForPreview(buildingType, _buildingConfig)
	print("🔍 Searching for building model:", buildingType)

	-- Try ReplicatedStorage first
	local replicatedModels = ReplicatedStorage:FindFirstChild("BuildingModels")
	if replicatedModels then
		print("✅ Found BuildingModels folder in ReplicatedStorage")

		-- List all available models for debugging
		local availableModels = {}
		for _, child in pairs(replicatedModels:GetChildren()) do
			if child:IsA("Model") then
				table.insert(availableModels, child.Name)
			end
		end
		print("📋 Available models:", table.concat(availableModels, ", "))

		-- Try exact match first
		local model = replicatedModels:FindFirstChild(buildingType)
		if model and model:IsA("Model") then
			local clonedModel = model:Clone()
			print("✅ Loaded exact model from ReplicatedStorage:", buildingType)
			return clonedModel
		end

		-- Try case-insensitive search
		for _, child in pairs(replicatedModels:GetChildren()) do
			if child:IsA("Model") and child.Name:lower() == buildingType:lower() then
				local clonedModel = child:Clone()
				print("✅ Loaded case-insensitive model from ReplicatedStorage:", child.Name, "for", buildingType)
				return clonedModel
			end
		end

		-- Try partial match search
		for _, child in pairs(replicatedModels:GetChildren()) do
			if child:IsA("Model") and (child.Name:find(buildingType) or buildingType:find(child.Name)) then
				local clonedModel = child:Clone()
				print("✅ Loaded partial match model from ReplicatedStorage:", child.Name, "for", buildingType)
				return clonedModel
			end
		end

		print("❌ No matching model found for:", buildingType)
	else
		print("❌ BuildingModels folder not found in ReplicatedStorage")

		-- Debug: List what's actually in ReplicatedStorage
		print("📋 ReplicatedStorage contents:")
		for _, child in pairs(ReplicatedStorage:GetChildren()) do
			print("  -", child.Name, "(" .. child.ClassName .. ")")
		end
	end

	print("🏗️ No actual model found for:", buildingType, "- will create enhanced fallback")
	return nil
end

-- Create enhanced fallback preview with better visuals and guaranteed visibility
function BuildingUI.CreateEnhancedFallbackPreview(buildingType, buildingConfig)
	local model = Instance.new("Model")
	model.Name = buildingType .. "FallbackPreview"

	-- Get building size with minimum size guarantee
	local size = buildingConfig.Size or {4, 4, 4}
	local buildingSize = Vector3.new(
		math.max(size[1], 2),
		math.max(size[2], 2),
		math.max(size[3], 2)
	)

	-- Create main building part with guaranteed visibility
	local mainPart = Instance.new("Part")
	mainPart.Name = "Base"
	mainPart.Anchored = true
	mainPart.CanCollide = false
	mainPart.Size = buildingSize
	mainPart.Position = Vector3.new(0, buildingSize.Y/2, 0)
	mainPart.Transparency = 0 -- Ensure it's completely opaque
	mainPart.CastShadow = false

	-- Enhanced materials and colors based on building type
	if buildingConfig.Type == Config.BUILDING_TYPES.RESIDENTIAL then
		mainPart.Color = Color3.new(0.8, 0.6, 0.4) -- Brown
		mainPart.Material = Enum.Material.Brick
		BuildingUI.AddResidentialDetails(model, mainPart, buildingSize)
	elseif buildingConfig.Type == Config.BUILDING_TYPES.COMMERCIAL then
		mainPart.Color = Color3.new(0.2, 0.6, 0.8) -- Blue
		mainPart.Material = Enum.Material.Glass
		BuildingUI.AddCommercialDetails(model, mainPart, buildingSize)
	elseif buildingConfig.Type == Config.BUILDING_TYPES.INDUSTRIAL then
		mainPart.Color = Color3.new(0.6, 0.6, 0.6) -- Gray
		mainPart.Material = Enum.Material.Metal
		BuildingUI.AddIndustrialDetails(model, mainPart, buildingSize)
	elseif buildingConfig.Type == Config.BUILDING_TYPES.SERVICE then
		mainPart.Color = Color3.new(0.8, 0.8, 0.2) -- Yellow
		mainPart.Material = Enum.Material.SmoothPlastic
		BuildingUI.AddServiceDetails(model, mainPart, buildingSize)
	elseif buildingConfig.Type == Config.BUILDING_TYPES.UTILITY then
		mainPart.Color = Color3.new(0.5, 0.5, 0.8) -- Purple
		mainPart.Material = Enum.Material.Neon
		BuildingUI.AddUtilityDetails(model, mainPart, buildingSize)
	else
		mainPart.Color = Color3.new(0.2, 0.8, 0.2) -- Green
		mainPart.Material = Enum.Material.Grass
		BuildingUI.AddDecorationDetails(model, mainPart, buildingSize)
	end

	mainPart.Parent = model
	model.PrimaryPart = mainPart

	return model
end

-- Add residential building details (windows, doors, etc.)
function BuildingUI.AddResidentialDetails(model, _mainPart, buildingSize)
	-- Add a roof
	local roof = Instance.new("Part")
	roof.Name = "Roof"
	roof.Anchored = true
	roof.CanCollide = false
	roof.Size = Vector3.new(buildingSize.X + 0.5, 1, buildingSize.Z + 0.5)
	roof.Position = Vector3.new(0, buildingSize.Y + 0.5, 0)
	roof.Color = Color3.new(0.5, 0.3, 0.2) -- Dark brown
	roof.Material = Enum.Material.Wood
	roof.Parent = model

	-- Add windows
	for i = 1, math.min(3, math.floor(buildingSize.X/2)) do
		local window = Instance.new("Part")
		window.Name = "Window" .. i
		window.Anchored = true
		window.CanCollide = false
		window.Size = Vector3.new(0.1, 1, 1)
		window.Position = Vector3.new(buildingSize.X/2 + 0.1, buildingSize.Y/2, -buildingSize.Z/2 + i * 2)
		window.Color = Color3.new(0.7, 0.9, 1) -- Light blue
		window.Material = Enum.Material.Glass
		window.Parent = model
	end
end

-- Add commercial building details (signs, glass, etc.)
function BuildingUI.AddCommercialDetails(model, _mainPart, buildingSize)
	-- Add a sign
	local sign = Instance.new("Part")
	sign.Name = "Sign"
	sign.Anchored = true
	sign.CanCollide = false
	sign.Size = Vector3.new(0.1, 1, buildingSize.Z * 0.8)
	sign.Position = Vector3.new(buildingSize.X/2 + 0.1, buildingSize.Y * 0.8, 0)
	sign.Color = Color3.new(1, 1, 0) -- Yellow
	sign.Material = Enum.Material.Neon
	sign.Parent = model

	-- Add glass front
	local glassFront = Instance.new("Part")
	glassFront.Name = "GlassFront"
	glassFront.Anchored = true
	glassFront.CanCollide = false
	glassFront.Size = Vector3.new(0.1, buildingSize.Y * 0.6, buildingSize.Z * 0.9)
	glassFront.Position = Vector3.new(buildingSize.X/2 + 0.05, buildingSize.Y * 0.3, 0)
	glassFront.Color = Color3.new(0.8, 0.9, 1) -- Light blue
	glassFront.Material = Enum.Material.Glass
	glassFront.Transparency = 0.3
	glassFront.Parent = model
end

-- Add industrial building details (pipes, smokestacks, etc.)
function BuildingUI.AddIndustrialDetails(model, _mainPart, buildingSize)
	-- Add smokestack
	local smokestack = Instance.new("Part")
	smokestack.Name = "Smokestack"
	smokestack.Anchored = true
	smokestack.CanCollide = false
	smokestack.Size = Vector3.new(1, buildingSize.Y * 0.8, 1)
	smokestack.Position = Vector3.new(buildingSize.X/3, buildingSize.Y + buildingSize.Y * 0.4, buildingSize.Z/3)
	smokestack.Color = Color3.new(0.3, 0.3, 0.3) -- Dark gray
	smokestack.Material = Enum.Material.Metal
	smokestack.Shape = Enum.PartType.Cylinder
	smokestack.Parent = model

	-- Add pipes
	local pipe = Instance.new("Part")
	pipe.Name = "Pipe"
	pipe.Anchored = true
	pipe.CanCollide = false
	pipe.Size = Vector3.new(0.5, 0.5, buildingSize.Z)
	pipe.Position = Vector3.new(-buildingSize.X/3, buildingSize.Y + 1, 0)
	pipe.Color = Color3.new(0.4, 0.4, 0.4) -- Gray
	pipe.Material = Enum.Material.Metal
	pipe.Shape = Enum.PartType.Cylinder
	pipe.Parent = model
end

-- Add service building details (cross, badge, etc.)
function BuildingUI.AddServiceDetails(model, _mainPart, buildingSize)
	-- Add service symbol on top
	local symbol = Instance.new("Part")
	symbol.Name = "ServiceSymbol"
	symbol.Anchored = true
	symbol.CanCollide = false
	symbol.Size = Vector3.new(1, 0.2, 1)
	symbol.Position = Vector3.new(0, buildingSize.Y + 0.5, 0)
	symbol.Color = Color3.new(1, 0, 0) -- Red
	symbol.Material = Enum.Material.Neon
	symbol.Shape = Enum.PartType.Cylinder
	symbol.Parent = model

	-- Add entrance
	local entrance = Instance.new("Part")
	entrance.Name = "Entrance"
	entrance.Anchored = true
	entrance.CanCollide = false
	entrance.Size = Vector3.new(0.1, buildingSize.Y * 0.4, 2)
	entrance.Position = Vector3.new(buildingSize.X/2 + 0.05, buildingSize.Y * 0.2, 0)
	entrance.Color = Color3.new(0.6, 0.4, 0.2) -- Brown
	entrance.Material = Enum.Material.Wood
	entrance.Parent = model
end

-- Add utility building details (antennas, transformers, etc.)
function BuildingUI.AddUtilityDetails(model, _mainPart, buildingSize)
	-- Add antenna/tower
	local antenna = Instance.new("Part")
	antenna.Name = "Antenna"
	antenna.Anchored = true
	antenna.CanCollide = false
	antenna.Size = Vector3.new(0.3, buildingSize.Y * 1.5, 0.3)
	antenna.Position = Vector3.new(0, buildingSize.Y + buildingSize.Y * 0.75, 0)
	antenna.Color = Color3.new(0.8, 0.8, 0.8) -- Light gray
	antenna.Material = Enum.Material.Metal
	antenna.Parent = model

	-- Add electrical effects
	local electricEffect = Instance.new("Part")
	electricEffect.Name = "ElectricEffect"
	electricEffect.Anchored = true
	electricEffect.CanCollide = false
	electricEffect.Size = Vector3.new(0.5, 0.5, 0.5)
	electricEffect.Position = Vector3.new(0, buildingSize.Y + buildingSize.Y * 1.2, 0)
	electricEffect.Color = Color3.new(0, 1, 1) -- Cyan
	electricEffect.Material = Enum.Material.Neon
	electricEffect.Shape = Enum.PartType.Ball
	electricEffect.Parent = model
end

-- Add decoration building details (trees, flowers, etc.)
function BuildingUI.AddDecorationDetails(model, _mainPart, buildingSize)
	-- Add tree-like structure for parks
	local tree = Instance.new("Part")
	tree.Name = "Tree"
	tree.Anchored = true
	tree.CanCollide = false
	tree.Size = Vector3.new(0.5, buildingSize.Y * 1.2, 0.5)
	tree.Position = Vector3.new(0, buildingSize.Y * 0.6, 0)
	tree.Color = Color3.new(0.4, 0.2, 0.1) -- Brown
	tree.Material = Enum.Material.Wood
	tree.Parent = model

	-- Add foliage
	local foliage = Instance.new("Part")
	foliage.Name = "Foliage"
	foliage.Anchored = true
	foliage.CanCollide = false
	foliage.Size = Vector3.new(buildingSize.X * 0.8, buildingSize.Y * 0.6, buildingSize.Z * 0.8)
	foliage.Position = Vector3.new(0, buildingSize.Y + buildingSize.Y * 0.3, 0)
	foliage.Color = Color3.new(0.1, 0.8, 0.1) -- Green
	foliage.Material = Enum.Material.Grass
	foliage.Shape = Enum.PartType.Ball
	foliage.Parent = model
end

-- Select building
function BuildingUI.SelectBuilding(buildingType, buildingConfig)
	print("🏗️ Selected building:", buildingConfig.Name)
	selectedBuilding = buildingType

	-- Update details panel
	local detailsPanel = buildingWindow:FindFirstChild("BuildingDetails")
	if detailsPanel then
		local infoSection = detailsPanel:FindFirstChild("BuildingInfo")
		if infoSection then
			-- Update name
			local nameLabel = infoSection:FindFirstChild("BuildingName")
			if nameLabel then
				nameLabel.Text = buildingConfig.Name
			end

			-- Update description with comprehensive building info
			local descLabel = infoSection:FindFirstChild("BuildingDescription")
			if descLabel then
				local description = buildingConfig.Description or ("A " .. (buildingConfig.Name or buildingType) .. " building")

				-- Add building stats
				local stats = {}
				if buildingConfig.Population then
					table.insert(stats, "👥 Population: " .. buildingConfig.Population)
				end
				if buildingConfig.EnergyProduction then
					table.insert(stats, "⚡ Produces: " .. buildingConfig.EnergyProduction .. " Energy")
				end
				if buildingConfig.EnergyConsumption then
					table.insert(stats, "⚡ Consumes: " .. buildingConfig.EnergyConsumption .. " Energy")
				end
				if buildingConfig.WaterProduction then
					table.insert(stats, "💧 Produces: " .. buildingConfig.WaterProduction .. " Water")
				end
				if buildingConfig.WaterConsumption then
					table.insert(stats, "💧 Consumes: " .. buildingConfig.WaterConsumption .. " Water")
				end
				if buildingConfig.BuildTime then
					table.insert(stats, "⏱️ Build Time: " .. buildingConfig.BuildTime .. "s")
				end
				if buildingConfig.MaxLevel then
					table.insert(stats, "📈 Max Level: " .. buildingConfig.MaxLevel)
				end

				if #stats > 0 then
					description = description .. "\n\n" .. table.concat(stats, "\n")
				end

				descLabel.Text = description
			end

			-- Update enhanced cost section
			local costSection = infoSection:FindFirstChild("CostSection")
			if costSection then
				-- Clear existing cost labels
				for _, child in ipairs(costSection:GetChildren()) do
					if child:IsA("TextLabel") then
						child:Destroy()
					end
				end

				-- Add cost title
				local costTitle = Instance.new("TextLabel")
				costTitle.Size = UDim2.new(1, -20, 0, 20)
				costTitle.Position = UDim2.new(0, 10, 0, 5)
				costTitle.BackgroundTransparency = 1
				costTitle.Text = "💰 Building Cost:"
				costTitle.TextColor3 = Color3.new(1, 1, 0.6)
				costTitle.TextSize = 14
				costTitle.Font = Enum.Font.SourceSansBold
				costTitle.TextXAlignment = Enum.TextXAlignment.Left
				costTitle.Parent = costSection

				-- Add cost labels
				local yOffset = 25
				if buildingConfig.Cost then
					for currency, amount in pairs(buildingConfig.Cost) do
						local costLabel = Instance.new("TextLabel")
						costLabel.Size = UDim2.new(1, -20, 0, 18)
						costLabel.Position = UDim2.new(0, 20, 0, yOffset)
						costLabel.BackgroundTransparency = 1

						local icon = "💰"
						if currency == "Cash" then
							icon = "💵"
							costLabel.Text = icon .. " $" .. amount
						elseif currency == "Pieces" then
							icon = "🧩"
							costLabel.Text = icon .. " " .. amount .. " Pieces"
						else
							costLabel.Text = icon .. " " .. amount .. " " .. currency
						end

						costLabel.TextColor3 = Color3.new(0.9, 0.9, 0.9)
						costLabel.TextSize = 12
						costLabel.Font = Enum.Font.SourceSans
						costLabel.TextXAlignment = Enum.TextXAlignment.Left
						costLabel.Parent = costSection
						yOffset = yOffset + 20
					end
				else
					local freeLabel = Instance.new("TextLabel")
					freeLabel.Size = UDim2.new(1, -20, 0, 18)
					freeLabel.Position = UDim2.new(0, 20, 0, yOffset)
					freeLabel.BackgroundTransparency = 1
					freeLabel.Text = "🎁 Free to build!"
					freeLabel.TextColor3 = Color3.new(0.6, 1, 0.6)
					freeLabel.TextSize = 12
					freeLabel.Font = Enum.Font.SourceSans
					freeLabel.TextXAlignment = Enum.TextXAlignment.Left
					freeLabel.Parent = costSection
				end
			end
		end

		-- Update 3D preview
		local viewport = detailsPanel:FindFirstChild("BuildingPreview")
		if viewport then
			-- Clear existing model
			for _, child in ipairs(viewport:GetChildren()) do
				if child:IsA("Model") then
					child:Destroy()
				end
			end
			-- Create new preview
			BuildingUI.CreateBuildingPreview(viewport, buildingType, buildingConfig)
		end
	end
end



-- Start building removal mode
function BuildingUI.StartBuildingRemoval(buildingType)
	print("🗑️ Starting removal mode for:", buildingType)

	-- Close building window
	BuildingUI.CloseBuildingWindow()

	-- Set removal mode
	local success, result = pcall(function()
		-- Try to integrate with existing building system
		if _G.ClientState then
			_G.ClientState.selectedBuildingType = buildingType
			_G.ClientState.buildingMode = false
			_G.ClientState.removalMode = true
		end

		-- Initialize mobile controls if on touch device
		if isTouchDevice then
			local MobileBuildingControls = require(script.Parent:WaitForChild("MobileBuildingControls"))
			MobileBuildingControls.StartRemovalMode(buildingType)
			print("📱 Mobile removal controls activated")
		end

		-- Fire remote event to start removal
		RemoteEvents.StartBuildingRemoval:FireServer(buildingType)
	end)

	if success then
		print("🗑️ Building removal mode started successfully")
		-- Show instruction to player (server will handle notification)
		print("🗑️ Click on a " .. buildingType .. " to remove it. Press Q to cancel.")
	else
		warn("🗑️ Failed to start building removal:", result)
	end
end

-- Enhanced building placement system with comprehensive debugging
function BuildingUI.StartBuildingPlacement(buildingType)
	print("🏗️ Starting enhanced placement for:", buildingType)

	-- Close building window
	BuildingUI.CloseBuildingWindow()

	-- Set building mode with comprehensive error handling
	local success, result = pcall(function()
		-- Debug: Check if RemoteEvents exists
		if not RemoteEvents then
			warn("❌ RemoteEvents not found! Building placement may not work.")
			print("💡 Make sure RemoteEvents is properly required in the script.")
			return
		end

		if not RemoteEvents.StartBuildingPlacement then
			warn("❌ StartBuildingPlacement remote event not found!")
			print("💡 Make sure the server has created this remote event.")
			return
		end

		-- Try to integrate with existing building system
		if _G.ClientState then
			_G.ClientState.selectedBuildingType = buildingType
			_G.ClientState.buildingMode = true
			_G.ClientState.removalMode = false
			print("✅ Updated ClientState for building mode")
		else
			print("⚠️ ClientState not found, creating basic state")
			_G.ClientState = {
				selectedBuildingType = buildingType,
				buildingMode = true,
				removalMode = false
			}
		end

		-- Initialize mobile controls if on touch device
		if isTouchDevice then
			local MobileBuildingControls = require(script.Parent:WaitForChild("MobileBuildingControls"))
			MobileBuildingControls.StartBuildingMode(buildingType)
			print("📱 Mobile building controls activated")
		end

		-- Fire remote event to start placement
		print("🔄 Firing StartBuildingPlacement remote event...")
		RemoteEvents.StartBuildingPlacement:FireServer(buildingType)
		print("✅ Remote event fired successfully")
	end)

	if success then
		print("🏗️ Enhanced building placement started successfully")
		print("🏗️ Instructions:")
		print("  - Click where you want to place the " .. buildingType)
		print("  - Press Q to cancel building")
		print("  - Press R/E to rotate building (if supported)")
	else
		warn("🏗️ Failed to start building placement:", result)
		print("🔧 Troubleshooting:")
		print("  1. Check if RemoteEvents is properly required")
		print("  2. Verify server has StartBuildingPlacement remote event")
		print("  3. Check if building system is properly initialized")
		print("  4. Try running _G.DebugBuildingPlacement() for more info")
	end
end

-- Debug function for building placement system
function BuildingUI.DebugBuildingPlacement()
	print("🔍 Building Placement Debug Info:")
	print("=" .. string.rep("=", 50))

	-- Check RemoteEvents
	print("📡 Remote Events:")
	if RemoteEvents then
		print("  ✅ RemoteEvents found")
		if RemoteEvents.StartBuildingPlacement then
			print("  ✅ StartBuildingPlacement remote event found")
		else
			print("  ❌ StartBuildingPlacement remote event NOT found")
		end
	else
		print("  ❌ RemoteEvents NOT found")
	end

	-- Check ClientState
	print("\n🎮 Client State:")
	if _G.ClientState then
		print("  ✅ ClientState found")
		print("  - selectedBuildingType:", _G.ClientState.selectedBuildingType or "nil")
		print("  - buildingMode:", _G.ClientState.buildingMode or "nil")
		print("  - removalMode:", _G.ClientState.removalMode or "nil")
	else
		print("  ❌ ClientState NOT found")
	end

	-- Check selected building
	print("\n🏗️ Selected Building:")
	if selectedBuilding then
		print("  ✅ Building selected:", selectedBuilding)
		local config = Config.BUILDINGS[selectedBuilding]
		if config then
			print("  ✅ Building config found")
			print("  - Name:", config.Name)
			print("  - Type:", config.Type)
			if config.Cost then
				print("  - Cost:", table.concat(config.Cost, ", "))
			end
		else
			print("  ❌ Building config NOT found")
		end
	else
		print("  ❌ No building selected")
	end

	print("\n💡 Solutions:")
	print("  1. Make sure to select a building first")
	print("  2. Check if server scripts are running")
	print("  3. Verify RemoteEvents are properly set up")
	print("  4. Try restarting the game if issues persist")
end

-- Make debug function globally accessible
_G.DebugBuildingPlacement = BuildingUI.DebugBuildingPlacement

-- Debug function to check UI state and available models
function BuildingUI.DebugUI()
	print("🔍 Building UI Debug Info:")
	print("  - PlayerGui exists:", playerGui ~= nil)
	print("  - UrbanSimUI exists:", playerGui:FindFirstChild("UrbanSimUI") ~= nil)
	print("  - Building button exists:", playerGui:FindFirstChild("UrbanSimUI") and playerGui.UrbanSimUI:FindFirstChild("EnhancedBuildingButton") ~= nil)
	print("  - Building window exists:", buildingWindow ~= nil)
	print("  - Window is open:", isOpen)

	local screenSize = workspace.CurrentCamera.ViewportSize
	print("  - Screen size:", screenSize.X, "x", screenSize.Y)

	local windowWidth, windowHeight, containerHeight = getWindowDimensions()
	print("  - Calculated window size:", windowWidth, "x", windowHeight)
	print("  - Container height:", containerHeight)

	-- Debug building models
	print("\n🏗️ Building Models Debug:")
	local replicatedModels = ReplicatedStorage:FindFirstChild("BuildingModels")
	if replicatedModels then
		print("✅ BuildingModels folder found in ReplicatedStorage")
		local modelCount = 0
		for _, child in pairs(replicatedModels:GetChildren()) do
			if child:IsA("Model") then
				modelCount = modelCount + 1
				print("  📦 Model:", child.Name)
			end
		end
		print("  📊 Total models found:", modelCount)
	else
		print("❌ BuildingModels folder NOT found in ReplicatedStorage")
		print("📋 ReplicatedStorage contents:")
		for _, child in pairs(ReplicatedStorage:GetChildren()) do
			print("  -", child.Name, "(" .. child.ClassName .. ")")
		end
	end
end

-- Function to list all available building models
function BuildingUI.ListAvailableModels()
	print("📋 Available Building Models:")
	local replicatedModels = ReplicatedStorage:FindFirstChild("BuildingModels")
	if replicatedModels then
		local models = {}
		for _, child in pairs(replicatedModels:GetChildren()) do
			if child:IsA("Model") then
				table.insert(models, child.Name)
			end
		end

		if #models > 0 then
			print("✅ Found", #models, "models:")
			for i, modelName in ipairs(models) do
				print("  " .. i .. ". " .. modelName)
			end
		else
			print("❌ No models found in BuildingModels folder")
		end
	else
		print("❌ BuildingModels folder not found in ReplicatedStorage")
		print("💡 Create the folder and add your building models there!")
	end
end

-- Console command to debug models (users can run this in the developer console)
function BuildingUI.DebugModels()
	print("🔧 Building Models Debug Command")
	print("=" .. string.rep("=", 40))

	BuildingUI.ListAvailableModels()

	-- Test loading a few common building types
	print("\n🧪 Testing model loading:")
	local testBuildings = {"HOUSE_SMALL", "HOUSE_MEDIUM", "APARTMENT", "SHOP_SMALL", "POWER_PLANT", "WATER_PLANT"}

	for _, buildingType in ipairs(testBuildings) do
		local buildingConfig = Config.BUILDINGS[buildingType]
		if buildingConfig then
			local model = BuildingUI.LoadBuildingModelForPreview(buildingType, buildingConfig)
			if model then
				print("✅ " .. buildingType .. " loaded successfully")
				model:Destroy() -- Clean up test model
			else
				print("❌ " .. buildingType .. " failed to load")
			end
		else
			print("⚠️  " .. buildingType .. " not found in Config.BUILDINGS")
		end
	end

	print("\n💡 SOLUTIONS:")
	print("  1. Run FIX_BUILDING_MODELS.lua in Roblox Studio")
	print("  2. Make sure models are in ReplicatedStorage > BuildingModels")
	print("  3. Check model names match exactly with building types")
	print("  4. Ensure models have parts and are properly structured")
end

-- Make debug function globally accessible
_G.DebugBuildingModels = BuildingUI.DebugModels

-- Debug function to check BuildingGrid and ScrollFrame
function BuildingUI.DebugBuildingGrid()
	print("🔍 Building Grid Debug Info:")
	print("=" .. string.rep("=", 40))

	if not buildingWindow then
		print("❌ Building window not found!")
		return
	end

	local buildingGrid = buildingWindow:FindFirstChild("BuildingGrid")
	if not buildingGrid then
		print("❌ BuildingGrid not found!")
		print("📋 BuildingWindow children:")
		for _, child in pairs(buildingWindow:GetChildren()) do
			print("  -", child.Name, "(" .. child.ClassName .. ")")
		end
		return
	end

	print("✅ BuildingGrid found")
	print("  - Size:", buildingGrid.AbsoluteSize)
	print("  - Position:", buildingGrid.AbsolutePosition)
	print("  - Visible:", buildingGrid.Visible)
	print("  - ZIndex:", buildingGrid.ZIndex)

	local scrollFrame = buildingGrid:FindFirstChild("BuildingScrollFrame")
	if not scrollFrame then
		print("❌ BuildingScrollFrame not found!")
		print("📋 BuildingGrid children:")
		for _, child in pairs(buildingGrid:GetChildren()) do
			print("  -", child.Name, "(" .. child.ClassName .. ")")
		end
		return
	end

	print("✅ BuildingScrollFrame found")
	print("  - Size:", scrollFrame.AbsoluteSize)
	print("  - Position:", scrollFrame.AbsolutePosition)
	print("  - Visible:", scrollFrame.Visible)
	print("  - CanvasSize:", scrollFrame.CanvasSize)
	print("  - ZIndex:", scrollFrame.ZIndex)

	local gridLayout = scrollFrame:FindFirstChildOfClass("UIGridLayout")
	if gridLayout then
		print("✅ UIGridLayout found")
		print("  - CellSize:", gridLayout.CellSize)
		print("  - CellPadding:", gridLayout.CellPadding)
		print("  - AbsoluteContentSize:", gridLayout.AbsoluteContentSize)
	else
		print("❌ UIGridLayout not found!")
	end

	print("📊 ScrollFrame children count:", #scrollFrame:GetChildren())
	for i, child in pairs(scrollFrame:GetChildren()) do
		if child:IsA("Frame") and child.Name:find("BuildingCard") then
			print("  📦 " .. i .. ". " .. child.Name .. " - Size: " .. tostring(child.AbsoluteSize))
		end
	end
end

-- Make BuildingGrid debug function globally accessible
_G.DebugBuildingGrid = BuildingUI.DebugBuildingGrid

-- Comprehensive building system debug function
function BuildingUI.DebugBuildingSystem()
	print("🔍 Complete Building System Debug:")
	print("=" .. string.rep("=", 60))

	-- 1. Check RemoteEvents and RemoteFunctions
	print("\n📡 Remote Communication:")
	local remoteChecks = {
		{RemoteEvents, "StartBuildingPlacement", "RemoteEvent"},
		{RemoteEvents, "PlaceBuilding", "RemoteEvent"},
		{RemoteEvents, "RemoveBuilding", "RemoteEvent"},
		{RemoteFunctions, "CanPlaceBuilding", "RemoteFunction"},
		{RemoteFunctions, "CanBuild", "RemoteFunction"}
	}

	for _, check in ipairs(remoteChecks) do
		local container, name, type = check[1], check[2], check[3]
		if container and container[name] then
			print("  ✅", name, "(" .. type .. ") - Found")
		else
			print("  ❌", name, "(" .. type .. ") - Missing")
		end
	end

	-- 2. Check ClientState
	print("\n🎮 Client State:")
	if _G.ClientState then
		print("  ✅ ClientState found")
		print("    - selectedBuildingType:", _G.ClientState.selectedBuildingType or "nil")
		print("    - buildingMode:", _G.ClientState.buildingMode or "nil")
		print("    - buildingRotation:", _G.ClientState.buildingRotation or "nil")
	else
		print("  ❌ ClientState not found")
	end

	-- 3. Check BuildingUI state
	print("\n🏗️ BuildingUI State:")
	print("  - selectedBuilding:", selectedBuilding or "nil")
	print("  - buildingWindow exists:", buildingWindow and "YES" or "NO")
	if buildingWindow then
		print("  - buildingWindow visible:", buildingWindow.Visible)
	end

	-- 4. Check Config and BuildingSystem
	print("\n📋 Configuration:")
	if Config then
		print("  ✅ Config loaded")
		if Config.BUILDINGS then
			local buildingCount = 0
			for _ in pairs(Config.BUILDINGS) do
				buildingCount = buildingCount + 1
			end
			print("  - Buildings defined:", buildingCount)
		else
			print("  ❌ Config.BUILDINGS not found")
		end
	else
		print("  ❌ Config not loaded")
	end

	if BuildingSystem then
		print("  ✅ BuildingSystem loaded")
	else
		print("  ❌ BuildingSystem not loaded")
	end

	-- 5. Test building selection and placement
	print("\n🧪 System Test:")
	if selectedBuilding then
		local config = Config.BUILDINGS[selectedBuilding]
		if config then
			print("  ✅ Selected building config found:", config.Name)
			print("    - Cost:", config.Cost and "defined" or "missing")
			print("    - UnlockLevel:", config.UnlockLevel or "missing")
		else
			print("  ❌ Selected building config missing")
		end
	else
		print("  ⚠️ No building selected for testing")
	end

	print("\n💡 Quick Actions:")
	print("  - _G.DebugBuildingPlacement() - Debug placement system")
	print("  - _G.DebugBuildingGrid() - Debug building grid")
	print("  - _G.DebugBuildingModels() - Debug building models")
	print("  - BuildingUI.ShowBuildingWindow() - Open building window")
end

-- Debug function to check Config.BUILDINGS
function BuildingUI.DebugConfigBuildings()
	print("🔍 DEBUG: Checking Config.BUILDINGS...")

	if not Config then
		warn("❌ Config module not loaded!")
		return
	end

	if not Config.BUILDINGS then
		warn("❌ Config.BUILDINGS not found!")
		return
	end

	local buildingCount = 0
	for _ in pairs(Config.BUILDINGS) do
		buildingCount = buildingCount + 1
	end

	print("✅ Config.BUILDINGS found with", buildingCount, "buildings")

	-- Check for the specific missing buildings
	local missingBuildings = {"WOOD_FACTORY", "ELECTRONICS_FACTORY", "TECH_FACTORY"}

	for _, buildingType in ipairs(missingBuildings) do
		if Config.BUILDINGS[buildingType] then
			print("✅ Found:", buildingType, "=", Config.BUILDINGS[buildingType].Name)
		else
			warn("❌ Missing:", buildingType)
		end
	end

	-- Print all factory buildings
	print("🏭 All factory buildings in Config.BUILDINGS:")
	for buildingType, buildingConfig in pairs(Config.BUILDINGS) do
		if buildingType:find("FACTORY") then
			print("  -", buildingType, "=", buildingConfig.Name)
		end
	end
end

-- Make comprehensive debug function globally accessible
_G.DebugBuildingSystem = BuildingUI.DebugBuildingSystem

-- Initialize building UI
function BuildingUI.Initialize()
	print("🏗️ Initializing Enhanced Building UI...")

	-- Debug Config.BUILDINGS first
	BuildingUI.DebugConfigBuildings()

	-- Wait for UrbanSimUI to be created
	local screenGui = playerGui:WaitForChild("UrbanSimUI", 10)
	if not screenGui then
		warn("🏗️ Failed to find UrbanSimUI after 10 seconds!")
		BuildingUI.DebugUI()
		return
	end

	-- Small delay to ensure UI is fully loaded
	task.wait(0.1)

	local success, result = pcall(function()
		return BuildingUI.CreateBuildingButton()
	end)

	if success and result then
		print("🏗️ Enhanced Building UI initialized successfully!")
		BuildingUI.DebugUI()
	else
		warn("🏗️ Failed to initialize Building UI:", result)
		BuildingUI.DebugUI()
	end
end

return BuildingUI
