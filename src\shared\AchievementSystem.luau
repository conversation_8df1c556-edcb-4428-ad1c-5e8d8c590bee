--[[
	Achievement System
	Comprehensive achievement tracking and rewards
]]

local AchievementSystem = {}

-- Achievement definitions
AchievementSystem.ACHIEVEMENTS = {
	-- Building Achievements
	FIRST_BUILDER = {
		Id = "FIRST_BUILDER",
		Name = "First Builder",
		Description = "Place your first building",
		Icon = "🏗️",
		Type = "Building",
		Requirement = {Type = "BuildingsPlaced", Count = 1},
		Reward = {Pieces = 500, XP = 100},
		Rarity = "Common"
	},
	
	CITY_PLANNER = {
		Id = "CITY_PLANNER",
		Name = "City Planner",
		Description = "Place 10 buildings",
		Icon = "🏙️",
		Type = "Building",
		Requirement = {Type = "BuildingsPlaced", Count = 10},
		Reward = {Pieces = 2000, XP = 500, Cles = 1},
		Rarity = "Uncommon"
	},
	
	METROPOLIS_MAYOR = {
		Id = "METROPOLIS_MAYOR",
		Name = "Metropolis Mayor",
		Description = "Place 50 buildings",
		Icon = "🌆",
		Type = "Building",
		Requirement = {Type = "BuildingsPlaced", Count = 50},
		Reward = {Pieces = 10000, XP = 2000, ClesDiamant = 1},
		Rarity = "Rare"
	},
	
	-- Population Achievements
	SMALL_TOWN = {
		Id = "SMALL_TOWN",
		Name = "Small Town",
		Description = "Reach 100 population",
		Icon = "👥",
		Type = "Population",
		Requirement = {Type = "Population", Count = 100},
		Reward = {Pieces = 1000, XP = 200},
		Rarity = "Common"
	},
	
	BIG_CITY = {
		Id = "BIG_CITY",
		Name = "Big City",
		Description = "Reach 1000 population",
		Icon = "🏘️",
		Type = "Population",
		Requirement = {Type = "Population", Count = 1000},
		Reward = {Pieces = 5000, XP = 1000, Cash = 10},
		Rarity = "Uncommon"
	},
	
	MEGACITY = {
		Id = "MEGACITY",
		Name = "Megacity",
		Description = "Reach 10000 population",
		Icon = "🌃",
		Type = "Population",
		Requirement = {Type = "Population", Count = 10000},
		Reward = {Pieces = 25000, XP = 5000, GammaCoin = 1},
		Rarity = "Epic"
	},
	
	-- Crafting Achievements
	FIRST_CRAFT = {
		Id = "FIRST_CRAFT",
		Name = "First Craft",
		Description = "Complete your first crafting recipe",
		Icon = "🔨",
		Type = "Crafting",
		Requirement = {Type = "ItemsCrafted", Count = 1},
		Reward = {Pieces = 300, XP = 50},
		Rarity = "Common"
	},
	
	MASTER_CRAFTER = {
		Id = "MASTER_CRAFTER",
		Name = "Master Crafter",
		Description = "Craft 100 items",
		Icon = "⚒️",
		Type = "Crafting",
		Requirement = {Type = "ItemsCrafted", Count = 100},
		Reward = {Pieces = 5000, XP = 1000, CarteMere = 5},
		Rarity = "Rare"
	},
	
	INDUSTRIAL_TYCOON = {
		Id = "INDUSTRIAL_TYCOON",
		Name = "Industrial Tycoon",
		Description = "Craft 1000 items",
		Icon = "🏭",
		Type = "Crafting",
		Requirement = {Type = "ItemsCrafted", Count = 1000},
		Reward = {Pieces = 20000, XP = 4000, PC = 10},
		Rarity = "Epic"
	},
	
	-- Daily Achievements
	DAILY_VISITOR = {
		Id = "DAILY_VISITOR",
		Name = "Daily Visitor",
		Description = "Log in for 7 consecutive days",
		Icon = "📅",
		Type = "Daily",
		Requirement = {Type = "LoginStreak", Count = 7},
		Reward = {Pieces = 3000, XP = 500, Cles = 2},
		Rarity = "Uncommon"
	},
	
	DEDICATED_MAYOR = {
		Id = "DEDICATED_MAYOR",
		Name = "Dedicated Mayor",
		Description = "Log in for 30 consecutive days",
		Icon = "🗓️",
		Type = "Daily",
		Requirement = {Type = "LoginStreak", Count = 30},
		Reward = {Pieces = 15000, XP = 3000, ClesDiamant = 2, Cash = 50},
		Rarity = "Epic"
	},
	
	-- Economic Achievements
	FIRST_MILLION = {
		Id = "FIRST_MILLION",
		Name = "First Million",
		Description = "Accumulate 1,000,000 pieces",
		Icon = "💰",
		Type = "Economic",
		Requirement = {Type = "TotalPiecesEarned", Count = 1000000},
		Reward = {Cash = 25, XP = 2000},
		Rarity = "Rare"
	},
	
	BILLIONAIRE = {
		Id = "BILLIONAIRE",
		Name = "Billionaire",
		Description = "Accumulate 1,000,000,000 pieces",
		Icon = "💎",
		Type = "Economic",
		Requirement = {Type = "TotalPiecesEarned", Count = 1000000000},
		Reward = {GammaCoin = 5, Cash = 100, XP = 10000},
		Rarity = "Legendary"
	},
	
	-- Mission Achievements
	DISASTER_RESPONSE = {
		Id = "DISASTER_RESPONSE",
		Name = "Disaster Response",
		Description = "Complete your first disaster mission",
		Icon = "🚨",
		Type = "Mission",
		Requirement = {Type = "MissionsCompleted", Count = 1},
		Reward = {Pieces = 1000, XP = 200, Metal = 5},
		Rarity = "Common"
	},
	
	CRISIS_MANAGER = {
		Id = "CRISIS_MANAGER",
		Name = "Crisis Manager",
		Description = "Complete 25 disaster missions",
		Icon = "🛡️",
		Type = "Mission",
		Requirement = {Type = "MissionsCompleted", Count = 25},
		Reward = {Pieces = 10000, XP = 2000, ClesDiamant = 1},
		Rarity = "Rare"
	},
	
	-- Special Achievements
	SPEED_BUILDER = {
		Id = "SPEED_BUILDER",
		Name = "Speed Builder",
		Description = "Place 10 buildings in under 5 minutes",
		Icon = "⚡",
		Type = "Special",
		Requirement = {Type = "BuildingsInTime", Count = 10, Time = 300},
		Reward = {Pieces = 5000, XP = 1000, Cash = 15},
		Rarity = "Rare"
	},
	
	PERFECTIONIST = {
		Id = "PERFECTIONIST",
		Name = "Perfectionist",
		Description = "Complete 10 crafting recipes without canceling any",
		Icon = "✨",
		Type = "Special",
		Requirement = {Type = "PerfectCrafting", Count = 10},
		Reward = {Pieces = 3000, XP = 600, CarteMere = 3},
		Rarity = "Uncommon"
	}
}

-- Achievement rarity colors
AchievementSystem.RARITY_COLORS = {
	Common = Color3.new(0.7, 0.7, 0.7),      -- Gray
	Uncommon = Color3.new(0.2, 0.8, 0.2),    -- Green
	Rare = Color3.new(0.2, 0.6, 1),          -- Blue
	Epic = Color3.new(0.8, 0.2, 1),          -- Purple
	Legendary = Color3.new(1, 0.8, 0.2),     -- Gold
	Mythic = Color3.new(1, 0.2, 0.2)         -- Red
}

-- Check if achievement is unlocked
function AchievementSystem.CheckAchievement(achievementId, playerData)
	local achievement = AchievementSystem.ACHIEVEMENTS[achievementId]
	if not achievement then return false end
	
	-- Check if already unlocked
	if playerData.UnlockedAchievements and playerData.UnlockedAchievements[achievementId] then
		return false
	end
	
	local requirement = achievement.Requirement
	local playerStats = playerData.AchievementStats or {}
	
	-- Check different requirement types
	if requirement.Type == "BuildingsPlaced" then
		return (playerStats.BuildingsPlaced or 0) >= requirement.Count
	elseif requirement.Type == "Population" then
		return (playerData.Population or 0) >= requirement.Count
	elseif requirement.Type == "ItemsCrafted" then
		return (playerStats.ItemsCrafted or 0) >= requirement.Count
	elseif requirement.Type == "LoginStreak" then
		return (playerData.DailyStreak or 0) >= requirement.Count
	elseif requirement.Type == "TotalPiecesEarned" then
		return (playerStats.TotalPiecesEarned or 0) >= requirement.Count
	elseif requirement.Type == "MissionsCompleted" then
		return (playerStats.MissionsCompleted or 0) >= requirement.Count
	elseif requirement.Type == "BuildingsInTime" then
		-- Special time-based achievement
		return AchievementSystem.CheckTimedAchievement(requirement, playerStats)
	elseif requirement.Type == "PerfectCrafting" then
		return (playerStats.PerfectCrafting or 0) >= requirement.Count
	end
	
	return false
end

-- Check timed achievement
function AchievementSystem.CheckTimedAchievement(requirement, playerStats)
	local recentBuildings = playerStats.RecentBuildings or {}
	local currentTime = tick()
	local validBuildings = 0
	
	for _, buildTime in ipairs(recentBuildings) do
		if currentTime - buildTime <= requirement.Time then
			validBuildings = validBuildings + 1
		end
	end
	
	return validBuildings >= requirement.Count
end

-- Update achievement stats
function AchievementSystem.UpdateStats(playerData, statType, value)
	if not playerData.AchievementStats then
		playerData.AchievementStats = {}
	end
	
	local stats = playerData.AchievementStats
	
	if statType == "BuildingPlaced" then
		stats.BuildingsPlaced = (stats.BuildingsPlaced or 0) + 1
		
		-- Track recent buildings for timed achievements
		if not stats.RecentBuildings then
			stats.RecentBuildings = {}
		end
		table.insert(stats.RecentBuildings, tick())
		
		-- Keep only last 20 buildings
		if #stats.RecentBuildings > 20 then
			table.remove(stats.RecentBuildings, 1)
		end
		
	elseif statType == "ItemCrafted" then
		stats.ItemsCrafted = (stats.ItemsCrafted or 0) + (value or 1)
		
	elseif statType == "PiecesEarned" then
		stats.TotalPiecesEarned = (stats.TotalPiecesEarned or 0) + value
		
	elseif statType == "MissionCompleted" then
		stats.MissionsCompleted = (stats.MissionsCompleted or 0) + 1
		
	elseif statType == "PerfectCraft" then
		stats.PerfectCrafting = (stats.PerfectCrafting or 0) + 1
		
	elseif statType == "CraftCanceled" then
		-- Reset perfect crafting streak
		stats.PerfectCrafting = 0
	end
end

-- Get all available achievements for player
function AchievementSystem.GetAvailableAchievements(playerData)
	local available = {}
	local unlocked = playerData.UnlockedAchievements or {}
	
	for achievementId, achievement in pairs(AchievementSystem.ACHIEVEMENTS) do
		if not unlocked[achievementId] then
			local progress = AchievementSystem.GetAchievementProgress(achievementId, playerData)
			table.insert(available, {
				Achievement = achievement,
				Progress = progress,
				CanUnlock = progress >= 1
			})
		end
	end
	
	-- Sort by progress (closest to completion first)
	table.sort(available, function(a, b)
		return a.Progress > b.Progress
	end)
	
	return available
end

-- Get achievement progress (0-1)
function AchievementSystem.GetAchievementProgress(achievementId, playerData)
	local achievement = AchievementSystem.ACHIEVEMENTS[achievementId]
	if not achievement then return 0 end
	
	local requirement = achievement.Requirement
	local playerStats = playerData.AchievementStats or {}
	local current = 0
	
	-- Get current progress based on requirement type
	if requirement.Type == "BuildingsPlaced" then
		current = playerStats.BuildingsPlaced or 0
	elseif requirement.Type == "Population" then
		current = playerData.Population or 0
	elseif requirement.Type == "ItemsCrafted" then
		current = playerStats.ItemsCrafted or 0
	elseif requirement.Type == "LoginStreak" then
		current = playerData.DailyStreak or 0
	elseif requirement.Type == "TotalPiecesEarned" then
		current = playerStats.TotalPiecesEarned or 0
	elseif requirement.Type == "MissionsCompleted" then
		current = playerStats.MissionsCompleted or 0
	elseif requirement.Type == "PerfectCrafting" then
		current = playerStats.PerfectCrafting or 0
	end
	
	return math.min(current / requirement.Count, 1)
end

-- Unlock achievement
function AchievementSystem.UnlockAchievement(playerData, achievementId)
	if not playerData.UnlockedAchievements then
		playerData.UnlockedAchievements = {}
	end
	
	playerData.UnlockedAchievements[achievementId] = {
		UnlockedAt = tick(),
		Id = achievementId
	}
	
	local achievement = AchievementSystem.ACHIEVEMENTS[achievementId]
	return achievement
end

-- Get unlocked achievements count by rarity
function AchievementSystem.GetUnlockedByRarity(playerData)
	local counts = {
		Common = 0,
		Uncommon = 0,
		Rare = 0,
		Epic = 0,
		Legendary = 0,
		Mythic = 0
	}
	
	local unlocked = playerData.UnlockedAchievements or {}
	
	for achievementId, _ in pairs(unlocked) do
		local achievement = AchievementSystem.ACHIEVEMENTS[achievementId]
		if achievement then
			counts[achievement.Rarity] = counts[achievement.Rarity] + 1
		end
	end
	
	return counts
end

-- Calculate total achievement score
function AchievementSystem.GetAchievementScore(playerData)
	local score = 0
	local rarityPoints = {
		Common = 10,
		Uncommon = 25,
		Rare = 50,
		Epic = 100,
		Legendary = 250,
		Mythic = 500
	}
	
	local unlocked = playerData.UnlockedAchievements or {}
	
	for achievementId, _ in pairs(unlocked) do
		local achievement = AchievementSystem.ACHIEVEMENTS[achievementId]
		if achievement then
			score = score + (rarityPoints[achievement.Rarity] or 0)
		end
	end
	
	return score
end

return AchievementSystem
